// 测试订单修改功能的完整性
const orderId = 6;

// 1. 获取原始订单数据
async function getOriginalOrder() {
  const response = await fetch(`http://localhost:3000/api/orders/${orderId}`);
  const data = await response.json();
  return data.data;
}

// 2. 测试仅修改备注信息
async function testNoteOnlyEdit(originalOrder) {
  console.log('\n=== 测试仅修改备注信息 ===');
  console.log('原始备注:', originalOrder.note || '无');
  
  const editData = {
    customerName: originalOrder.customer?.name || '',
    customerPhone: originalOrder.customer?.phone || '',
    isMember: originalOrder.customer?.isMember || false,
    memberNumber: originalOrder.customer?.memberNumber || '',
    note: '测试修改备注 - ' + new Date().toLocaleTimeString(),
    discountAmount: originalOrder.discount || 0,
    items: originalOrder.items || []
  };
  
  console.log('修改后的数据:');
  console.log('- 备注:', editData.note);
  console.log('- 药品数量:', editData.items.length);
  console.log('- 药品清单:', editData.items.map(item => `${item.name} x${item.quantity}`));
  
  const response = await fetch(`http://localhost:3000/api/orders/${orderId}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(editData)
  });
  
  const result = await response.json();
  console.log('修改结果:', result.success ? '成功' : '失败');
  if (!result.success) {
    console.log('错误信息:', result.message);
  }
  
  return result.success;
}

// 3. 验证修改后的数据
async function verifyAfterEdit() {
  console.log('\n=== 验证修改后的数据 ===');
  const response = await fetch(`http://localhost:3000/api/orders/${orderId}`);
  const data = await response.json();
  const order = data.data;
  
  console.log('验证结果:');
  console.log('- 订单ID:', order.id);
  console.log('- 备注:', order.note || '无');
  console.log('- 药品数量:', order.items?.length || 0);
  console.log('- 药品清单:', order.items?.map(item => `${item.name} x${item.quantity}`) || []);
  console.log('- 总金额:', order.total);
  console.log('- 优惠金额:', order.discount);
  
  return order.items?.length > 0;
}

// 主测试函数
async function runTest() {
  try {
    console.log('开始测试订单修改功能...');
    
    // 获取原始订单
    const originalOrder = await getOriginalOrder();
    console.log('原始订单数据:');
    console.log('- 订单ID:', originalOrder.id);
    console.log('- 药品数量:', originalOrder.items?.length || 0);
    console.log('- 药品清单:', originalOrder.items?.map(item => `${item.name} x${item.quantity}`) || []);
    
    // 测试仅修改备注
    const editSuccess = await testNoteOnlyEdit(originalOrder);
    
    if (editSuccess) {
      // 验证修改后的数据
      const dataIntact = await verifyAfterEdit();
      
      if (dataIntact) {
        console.log('\n✅ 测试通过：订单修改功能正常，数据完整性得到保证');
      } else {
        console.log('\n❌ 测试失败：修改后药品清单丢失');
      }
    } else {
      console.log('\n❌ 测试失败：订单修改操作失败');
    }
    
  } catch (error) {
    console.error('测试过程中发生错误:', error);
  }
}

// 运行测试
runTest();
