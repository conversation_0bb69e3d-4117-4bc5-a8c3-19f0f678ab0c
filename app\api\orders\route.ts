import { NextRequest, NextResponse } from 'next/server';
import { query, get, run } from '@/lib/db';

// 获取订单列表
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = (page - 1) * limit;

    // 获取订单总数 - 使用英文表名
    const countResult = await get('SELECT COUNT(*) as total FROM orders');
    const total = countResult?.total || 0;

    // 获取订单列表 - 使用英文表名，按订单编号降序排列（最新订单在前）
    const orders = await query(`
      SELECT
        o.id as id,
        o.order_number as orderNumber,
        o.total_amount as amount,
        o.status as status,
        o.created_at as date,
        c.name as customer,
        c.phone as phone
      FROM orders o
      LEFT JOIN customers c ON o.customer_id = c.id
      ORDER BY o.order_number DESC, o.created_at DESC
      LIMIT ? OFFSET ?
    `, [limit, offset]);

    // 获取今日销售统计 - 使用英文表名
    const today = new Date().toISOString().split('T')[0];
    const todayStats = await get(`
      SELECT
        COUNT(*) as orderCount,
        SUM(total_amount) as totalSales
      FROM orders
      WHERE date(created_at) = ?
    `, [today]);

    // 获取平均订单金额 - 使用英文表名
    const avgOrderAmount = await get(`
      SELECT AVG(total_amount) as average
      FROM orders
    `);

    // 获取热销商品 - 使用英文表名
    let topProducts = [];

    // 只有当有订单时才查询热销商品
    if (total > 0) {
      topProducts = await query(`
        SELECT
          p.name as name,
          SUM(od.quantity) as count,
          SUM(od.subtotal) as amount
        FROM order_details od
        JOIN products p ON od.product_id = p.id
        JOIN orders o ON od.order_id = o.id
        GROUP BY p.id
        ORDER BY count DESC
        LIMIT 5
      `);
    }

    return NextResponse.json({
      success: true,
      data: {
        orders,
        total,
        todayStats: {
          orderCount: todayStats.orderCount || 0,
          totalSales: todayStats.totalSales || 0
        },
        avgOrderAmount: avgOrderAmount.average || 0,
        topProducts
      }
    });
  } catch (error) {
    console.error('获取订单列表失败:', error);
    return NextResponse.json(
      { success: false, message: '获取订单列表失败' },
      { status: 500 }
    );
  }
}

// 创建新订单
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    const {
      orderNumber,
      customer,
      items,
      totalAmount,
      discountAmount = 0,
      payableAmount,
      receivedAmount,
      changeAmount = 0,
      paymentMethod,
      note
    } = data;

    // 开始事务
    await run('BEGIN TRANSACTION');

    try {
      // 1. 检查或创建客户 - 使用英文表名
      let customerId = null;
      if (customer && customer.name) {
        // 检查客户是否已存在
        const existingCustomer = await get(
          'SELECT id FROM customers WHERE phone = ?',
          [customer.phone]
        );

        if (existingCustomer) {
          customerId = existingCustomer.id;
        } else {
          // 创建新客户
          const result = await run(
            'INSERT INTO customers (name, phone) VALUES (?, ?)',
            [customer.name, customer.phone]
          );
          customerId = result.lastID;
        }
      }

      // 2. 创建订单 - 使用英文表名
      const orderResult = await run(
        `INSERT INTO orders (
          order_number, customer_id, operator_id, total_amount, discount_amount,
          payable_amount, received_amount, change_amount, payment_method, status, note
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          orderNumber,
          customerId,
          1, // 默认操作员ID，实际应该从认证中获取
          totalAmount,
          discountAmount,
          payableAmount,
          receivedAmount,
          changeAmount,
          paymentMethod || 'cash',
          data.status || 'completed',
          note || ''
        ]
      );

      const orderId = orderResult.lastID;

      // 3. 创建订单明细并更新库存 - 使用英文表名
      for (const item of items) {
        // 获取药品批次，这里简单处理，取最早过期的批次
        let batch = await get(
          `SELECT id FROM product_batches
           WHERE product_id = ? AND quantity > 0
           ORDER BY expiry_date ASC LIMIT 1`,
          [item.medicine.id]
        );

        // 如果没有可用批次，创建一个默认批次
        if (!batch) {
          console.log(`药品 ${item.medicine.name} 无可用批次，创建默认批次`);

          // 创建一个默认批次，有效期为当前日期后2年
          const today = new Date().toISOString().split('T')[0];
          const twoYearsLater = new Date();
          twoYearsLater.setFullYear(twoYearsLater.getFullYear() + 2);
          const expiryDate = twoYearsLater.toISOString().split('T')[0];

          const batchResult = await run(
            `INSERT INTO product_batches (
              product_id, batch_number, production_date, expiry_date, quantity, cost_price
            ) VALUES (?, ?, ?, ?, ?, ?)`,
            [
              item.medicine.id,
              `AUTO-${Date.now()}`, // 自动生成批次号
              today,
              expiryDate,
              item.quantity, // 设置初始数量为订单数量
              item.medicine.price * 0.7 // 默认成本价为售价的70%
            ]
          );

          // 获取新创建的批次ID
          batch = { id: batchResult.lastID };

          // 记录创建默认批次的日志
          console.log(`为药品 ${item.medicine.name} 创建了默认批次，ID: ${batch.id}`);
        }

        // 创建订单明细
        await run(
          `INSERT INTO order_details (
            order_id, product_id, batch_id, quantity, unit_price, subtotal
          ) VALUES (?, ?, ?, ?, ?, ?)`,
          [
            orderId,
            item.medicine.id,
            batch.id,
            item.quantity,
            item.medicine.price,
            item.subtotal
          ]
        );

        // 更新药品批次库存
        await run(
          `UPDATE product_batches SET quantity = quantity - ? WHERE id = ?`,
          [item.quantity, batch.id]
        );

        // 更新药品总库存
        await run(
          `UPDATE products SET stock_quantity = stock_quantity - ? WHERE id = ?`,
          [item.quantity, item.medicine.id]
        );

        // 记录库存变动 - 简化处理，不使用batch_id
        try {
          // 首先检查inventory_records表的结构
          const tableInfo = await query('PRAGMA table_info(inventory_records)');
          const columns = tableInfo.map(col => col.name);

          // 构建SQL语句和参数
          let sql, params;

          if (columns.includes('batch_id')) {
            // 如果表有batch_id列，使用它
            sql = `
              INSERT INTO inventory_records (
                product_id, batch_id, quantity_change, change_type, reference_id, reference_type, operator_id
              ) VALUES (?, ?, ?, ?, ?, ?, ?)
            `;
            params = [
              item.medicine.id,
              batch.id,
              -item.quantity, // 负数表示减少
              'sale',
              orderId,
              'order',
              1 // 默认操作员ID
            ];
          } else if (columns.includes('quantity_change')) {
            // 如果表有quantity_change列但没有batch_id
            sql = `
              INSERT INTO inventory_records (
                product_id, quantity_change, change_type, reference_id, reference_type, operator_id
              ) VALUES (?, ?, ?, ?, ?, ?)
            `;
            params = [
              item.medicine.id,
              -item.quantity, // 负数表示减少
              'sale',
              orderId,
              'order',
              1 // 默认操作员ID
            ];
          } else if (columns.includes('quantity')) {
            // 如果表使用quantity而不是quantity_change
            sql = `
              INSERT INTO inventory_records (
                product_id, quantity, type, reference_number, note
              ) VALUES (?, ?, ?, ?, ?)
            `;
            params = [
              item.medicine.id,
              -item.quantity, // 负数表示减少
              'out',
              `order-${orderNumber}`,
              `销售订单出库: ${orderNumber}`
            ];
          } else {
            // 如果表结构完全不匹配，记录日志但不中断流程
            console.log('inventory_records表结构不匹配，跳过库存记录');
            console.log('可用列:', columns.join(', '));
          }

          // 如果有SQL语句，执行插入
          if (sql && params) {
            await run(sql, params);
            console.log(`成功记录库存变动: 产品ID ${item.medicine.id}, 数量 ${-item.quantity}`);
          }
        } catch (inventoryError) {
          // 即使记录库存变动失败，也不要中断订单创建
          console.error('记录库存变动失败:', inventoryError);
          console.log('继续处理订单，但库存记录可能不完整');
        }
      }

      // 提交事务
      await run('COMMIT');

      return NextResponse.json({
        success: true,
        data: {
          orderId,
          orderNumber
        }
      });
    } catch (error) {
      // 回滚事务
      await run('ROLLBACK');
      throw error;
    }
  } catch (error) {
    console.error('创建订单失败:', error);
    return NextResponse.json(
      { success: false, message: `创建订单失败: ${error.message}` },
      { status: 500 }
    );
  }
}
