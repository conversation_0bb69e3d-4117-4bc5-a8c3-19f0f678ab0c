# Script to reset the SQLite database

# 定义相对于脚本位置的路径(项目根目录)
$dbPath = ".\db\retail.db"
$schemaPath = ".\db\schema.sql"

# 检查数据库文件是否存在并删除它
if (Test-Path $dbPath) {
    Write-Host "Removing existing database file: $dbPath"
    Remove-Item $dbPath -Force
} else {
    Write-Host "Database file not found: $dbPath"
}

# 从schema文件重新创建数据库
Write-Host "Recreating database from schema: $schemaPath"
# 确保sqlite3在PATH中或提供可执行文件的完整路径
# 使用调用运算符'&'并将.read命令和路径作为单个字符串参数传递
& sqlite3 $dbPath ".read '$schemaPath'"

# 检查命令是否执行成功(基本检查:文件是否存在)
if (Test-Path $dbPath) {
    Write-Host "Database reset successfully."
} else {
    Write-Host "Error: Database reset failed."
}

Write-Host "Script finished."