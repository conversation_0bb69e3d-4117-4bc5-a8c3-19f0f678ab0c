const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// 数据库文件路径
const dbPath = path.join(__dirname, '..', 'database.sqlite');
console.log('数据库路径:', dbPath);

const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('连接数据库失败:', err.message);
    return;
  }
  console.log('已连接到数据库');
});

// 创建测试数据
db.serialize(() => {
  // 创建测试客户
  db.run(`INSERT OR IGNORE INTO customers (id, name, phone, is_member, member_number) 
          VALUES (1, '张先生', '13812345678', 1, 'VIP10086')`, (err) => {
    if (err) console.error('创建客户失败:', err);
    else console.log('测试客户创建成功');
  });

  db.run(`INSERT OR IGNORE INTO customers (id, name, phone, is_member, member_number) 
          VALUES (2, '李女士', '13987654321', 0, NULL)`, (err) => {
    if (err) console.error('创建客户失败:', err);
    else console.log('测试客户2创建成功');
  });

  // 创建测试订单
  db.run(`INSERT OR IGNORE INTO orders (id, order_number, customer_id, operator_id, total_amount, discount_amount, payable_amount, received_amount, change_amount, payment_method, status, note, created_at) 
          VALUES (1, 'SO-202501050001', 1, 1, 92.1, 0, 92.1, 100, 7.9, 'wechat', 'completed', '客户要求配送', '2025-01-05 15:30:20')`, (err) => {
    if (err) console.error('创建订单失败:', err);
    else console.log('测试订单创建成功');
  });

  db.run(`INSERT OR IGNORE INTO orders (id, order_number, customer_id, operator_id, total_amount, discount_amount, payable_amount, received_amount, change_amount, payment_method, status, note, created_at) 
          VALUES (2, 'SO-202501050002', 2, 1, 45.5, 5, 40.5, 50, 9.5, 'cash', 'completed', '', '2025-01-05 16:20:15')`, (err) => {
    if (err) console.error('创建订单失败:', err);
    else console.log('测试订单2创建成功');
  });

  db.run(`INSERT OR IGNORE INTO orders (id, order_number, customer_id, operator_id, total_amount, discount_amount, payable_amount, received_amount, change_amount, payment_method, status, note, created_at) 
          VALUES (3, 'SO-202501050003', NULL, 1, 28.8, 0, 28.8, 30, 1.2, 'alipay', 'completed', '', '2025-01-05 17:10:30')`, (err) => {
    if (err) console.error('创建订单失败:', err);
    else console.log('测试订单3创建成功（散客）');
  });

  // 创建订单明细
  db.run(`INSERT OR IGNORE INTO order_details (id, order_id, product_id, batch_id, quantity, unit_price, subtotal) 
          VALUES (1, 1, 1, 1, 2, 36.8, 73.6)`, (err) => {
    if (err) console.error('创建订单明细失败:', err);
    else console.log('测试订单明细1创建成功');
  });

  db.run(`INSERT OR IGNORE INTO order_details (id, order_id, product_id, batch_id, quantity, unit_price, subtotal) 
          VALUES (2, 1, 2, 2, 1, 18.5, 18.5)`, (err) => {
    if (err) console.error('创建订单明细失败:', err);
    else console.log('测试订单明细2创建成功');
  });

  db.run(`INSERT OR IGNORE INTO order_details (id, order_id, product_id, batch_id, quantity, unit_price, subtotal) 
          VALUES (3, 2, 3, 3, 1, 45.5, 45.5)`, (err) => {
    if (err) console.error('创建订单明细失败:', err);
    else console.log('测试订单明细3创建成功');
  });

  db.run(`INSERT OR IGNORE INTO order_details (id, order_id, product_id, batch_id, quantity, unit_price, subtotal) 
          VALUES (4, 3, 4, 4, 2, 14.4, 28.8)`, (err) => {
    if (err) console.error('创建订单明细失败:', err);
    else console.log('测试订单明细4创建成功');
    
    // 关闭数据库连接
    db.close((err) => {
      if (err) {
        console.error('关闭数据库失败:', err.message);
      } else {
        console.log('数据库连接已关闭');
      }
    });
  });
});
