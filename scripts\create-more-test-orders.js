const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// 数据库文件路径
const dbPath = path.join(__dirname, '..', 'database.sqlite');
console.log('数据库路径:', dbPath);

const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('连接数据库失败:', err.message);
    return;
  }
  console.log('已连接到数据库');
});

// 创建更多测试订单来验证排序功能
db.serialize(() => {
  // 创建今天的更多订单，使用不同的订单编号
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, '0');
  const day = String(today.getDate()).padStart(2, '0');
  const dateStr = `${year}${month}${day}`;

  // 创建订单4 - 今天的第4个订单
  db.run(`INSERT OR IGNORE INTO orders (id, order_number, customer_id, operator_id, total_amount, discount_amount, payable_amount, received_amount, change_amount, payment_method, status, note, created_at) 
          VALUES (4, 'SO-${dateStr}0004', 1, 1, 156.8, 10, 146.8, 150, 3.2, 'wechat', 'completed', '会员优惠', '${year}-${month}-${day} 18:30:15')`, (err) => {
    if (err) console.error('创建订单4失败:', err);
    else console.log('测试订单4创建成功');
  });

  // 创建订单5 - 今天的第5个订单
  db.run(`INSERT OR IGNORE INTO orders (id, order_number, customer_id, operator_id, total_amount, discount_amount, payable_amount, received_amount, change_amount, payment_method, status, note, created_at) 
          VALUES (5, 'SO-${dateStr}0005', NULL, 1, 89.5, 0, 89.5, 100, 10.5, 'cash', 'completed', '', '${year}-${month}-${day} 19:15:30')`, (err) => {
    if (err) console.error('创建订单5失败:', err);
    else console.log('测试订单5创建成功（散客）');
  });

  // 创建订单6 - 今天的第6个订单
  db.run(`INSERT OR IGNORE INTO orders (id, order_number, customer_id, operator_id, total_amount, discount_amount, payable_amount, received_amount, change_amount, payment_method, status, note, created_at) 
          VALUES (6, 'SO-${dateStr}0006', 2, 1, 234.6, 15, 219.6, 220, 0.4, 'alipay', 'completed', '大额采购', '${year}-${month}-${day} 20:45:10')`, (err) => {
    if (err) console.error('创建订单6失败:', err);
    else console.log('测试订单6创建成功');
  });

  // 创建昨天的订单来测试日期排序
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  const yYear = yesterday.getFullYear();
  const yMonth = String(yesterday.getMonth() + 1).padStart(2, '0');
  const yDay = String(yesterday.getDate()).padStart(2, '0');
  const yDateStr = `${yYear}${yMonth}${yDay}`;

  db.run(`INSERT OR IGNORE INTO orders (id, order_number, customer_id, operator_id, total_amount, discount_amount, payable_amount, received_amount, change_amount, payment_method, status, note, created_at) 
          VALUES (7, 'SO-${yDateStr}0001', 1, 1, 67.8, 0, 67.8, 70, 2.2, 'wechat', 'completed', '昨日订单', '${yYear}-${yMonth}-${yDay} 16:20:30')`, (err) => {
    if (err) console.error('创建昨日订单失败:', err);
    else console.log('昨日测试订单创建成功');
  });

  // 为新订单创建订单明细
  db.run(`INSERT OR IGNORE INTO order_details (id, order_id, product_id, batch_id, quantity, unit_price, subtotal) 
          VALUES (5, 4, 1, 1, 3, 36.8, 110.4)`, (err) => {
    if (err) console.error('创建订单明细失败:', err);
    else console.log('订单4明细创建成功');
  });

  db.run(`INSERT OR IGNORE INTO order_details (id, order_id, product_id, batch_id, quantity, unit_price, subtotal) 
          VALUES (6, 4, 2, 2, 2, 23.2, 46.4)`, (err) => {
    if (err) console.error('创建订单明细失败:', err);
    else console.log('订单4明细2创建成功');
  });

  db.run(`INSERT OR IGNORE INTO order_details (id, order_id, product_id, batch_id, quantity, unit_price, subtotal) 
          VALUES (7, 5, 3, 3, 2, 44.75, 89.5)`, (err) => {
    if (err) console.error('创建订单明细失败:', err);
    else console.log('订单5明细创建成功');
  });

  db.run(`INSERT OR IGNORE INTO order_details (id, order_id, product_id, batch_id, quantity, unit_price, subtotal) 
          VALUES (8, 6, 1, 1, 4, 36.8, 147.2)`, (err) => {
    if (err) console.error('创建订单明细失败:', err);
    else console.log('订单6明细创建成功');
  });

  db.run(`INSERT OR IGNORE INTO order_details (id, order_id, product_id, batch_id, quantity, unit_price, subtotal) 
          VALUES (9, 6, 4, 4, 6, 14.4, 86.4)`, (err) => {
    if (err) console.error('创建订单明细失败:', err);
    else console.log('订单6明细2创建成功');
  });

  db.run(`INSERT OR IGNORE INTO order_details (id, order_id, product_id, batch_id, quantity, unit_price, subtotal) 
          VALUES (10, 7, 2, 2, 3, 22.6, 67.8)`, (err) => {
    if (err) console.error('创建订单明细失败:', err);
    else console.log('昨日订单明细创建成功');
    
    // 关闭数据库连接
    db.close((err) => {
      if (err) {
        console.error('关闭数据库失败:', err.message);
      } else {
        console.log('数据库连接已关闭');
      }
    });
  });
});
