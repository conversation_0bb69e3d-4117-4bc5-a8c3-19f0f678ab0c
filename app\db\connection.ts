import { Database } from 'sqlite3';
import { open } from 'sqlite';
import path from 'path';

let db: any = null;

export async function getDatabase() {
  if (!db) {
    const dbPath = path.join(process.cwd(), 'database.sqlite');
    db = await open({
      filename: dbPath,
      driver: Database
    });
  }
  return db;
}

export async function closeDatabase() {
  if (db) {
    await db.close();
    db = null;
  }
} 