# 药店零售管理系统数据库说明文档

本文档提供了药店零售管理系统数据库的详细说明，包括数据库结构、管理功能和使用指南。

## 数据库概述

系统使用SQLite3作为数据库引擎，具有以下特点：

- **轻量级**：无需安装独立的数据库服务器
- **零配置**：不需要复杂的配置过程
- **自包含**：所有数据存储在单个文件中，便于备份和恢复
- **跨平台**：支持Windows、macOS和Linux等多种操作系统

数据库文件位于系统根目录下的`database.sqlite`。

## 数据库结构

系统包含以下主要表：

### 1. 药品相关表

| 表名 | 英文表名 | 描述 |
|------|---------|------|
| 药品 | products | 存储药品基本信息 |
| 药品分类 | categories | 存储药品分类信息 |
| 药品批次 | product_batches | 存储药品批次信息，包括批号、生产日期、有效期等 |
| 药品附加信息 | product_details | 存储药品的附加信息，如说明书、注意事项等 |

### 2. 库存相关表

| 表名 | 英文表名 | 描述 |
|------|---------|------|
| 库存记录 | inventory_records | 记录药品入库、出库等库存变动 |
| 库存盘点 | inventory_checks | 记录库存盘点信息 |
| 库存盘点明细 | inventory_check_items | 记录库存盘点的明细项 |

### 3. 销售相关表

| 表名 | 英文表名 | 描述 |
|------|---------|------|
| 订单 | orders | 存储销售订单信息 |
| 订单明细 | order_details | 存储订单中的药品明细 |
| 客户 | customers | 存储客户信息 |
| 处方 | prescriptions | 存储处方信息 |
| 处方明细 | prescription_details | 存储处方中的药品明细 |

### 4. 供应商相关表

| 表名 | 英文表名 | 描述 |
|------|---------|------|
| 供应商 | suppliers | 存储供应商信息 |
| 采购订单 | purchase_orders | 存储采购订单信息 |
| 采购订单明细 | purchase_order_items | 存储采购订单中的药品明细 |

### 5. 系统相关表

| 表名 | 英文表名 | 描述 |
|------|---------|------|
| 用户 | users | 存储系统用户信息 |
| 系统设置 | settings | 存储系统配置信息 |
| 操作日志 | operation_logs | 记录系统操作日志 |

## 数据库管理功能

系统提供了以下数据库管理功能：

### 1. 初始化数据库

初始化数据库功能会创建所有必要的表结构。如果表已存在，将保留现有数据。此功能适用于：

- 系统首次使用时
- 数据库结构更新后
- 数据库文件损坏需要重建时

### 2. 数据库备份

数据库备份功能会创建当前数据库的完整副本，并保存到`backups`目录中。备份文件命名格式为`backup-YYYY-MM-DDTHH-mm-ss.sqlite`。

备份功能特点：

- 创建数据库的完整副本，包含所有表和数据
- 备份过程不会影响系统正常使用
- 备份文件可用于恢复数据库

### 3. 数据库恢复

数据库恢复功能允许从之前创建的备份中恢复数据库。恢复操作会完全替换当前的数据库文件。

**注意**：恢复操作会覆盖当前数据库中的所有数据，请谨慎操作。

## 使用指南

### 数据库初始化

1. 进入系统设置页面
2. 在"数据库管理"部分，点击"初始化数据库"按钮
3. 确认操作后，系统将创建所有必要的表结构

### 数据库备份

1. 进入系统设置页面
2. 在"数据库管理"部分，点击"备份数据库"按钮
3. 系统将创建数据库的完整备份，并显示备份成功消息

### 数据库恢复

1. 进入系统设置页面
2. 在"数据库管理"部分，查看备份列表
3. 点击要恢复的备份旁边的"恢复"按钮
4. 确认操作后，系统将从选定的备份中恢复数据库

### 备份管理建议

- **定期备份**：建议每天或每周定期备份数据库
- **重要操作前备份**：在进行重要操作（如批量导入数据）前进行备份
- **系统升级前备份**：在升级系统版本前进行备份
- **外部存储**：定期将备份文件复制到外部存储设备或云存储服务

## 数据库文件位置

- 主数据库文件：`./database.sqlite`
- 备份文件目录：`./backups/`

## 技术实现

系统使用以下技术实现数据库功能：

- **SQLite3**：轻量级关系型数据库引擎
- **sqlite**：Node.js SQLite客户端库
- **sqlite3**：Node.js SQLite3驱动程序
- **Next.js API Routes**：提供数据库操作的API接口

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库文件是否存在
   - 确保应用有足够的权限访问数据库文件
   - 尝试初始化数据库

2. **数据库备份失败**
   - 确保`backups`目录存在且可写
   - 检查磁盘空间是否充足

3. **数据库恢复失败**
   - 确保选择的备份文件完整且未损坏
   - 确保应用有足够的权限替换数据库文件

### 数据库修复

如果数据库文件损坏，可以尝试以下修复步骤：

1. 使用最近的备份恢复数据库
2. 如果没有可用的备份，尝试初始化数据库（注意：这将创建新的空数据库）

## 开发者信息

数据库相关代码位于以下目录：

- `app/db/`：数据库初始化和连接代码
- `lib/db.ts`：数据库操作工具函数
- `app/api/db-management/`：数据库管理API
- `app/settings/components/DatabaseManagement.tsx`：数据库管理UI组件
