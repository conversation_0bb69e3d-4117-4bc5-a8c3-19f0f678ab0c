const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// 打开数据库连接
const db = new sqlite3.Database(path.join(__dirname, '..', 'database.sqlite'), (err) => {
  if (err) {
    console.error('打开数据库失败:', err.message);
    process.exit(1);
  }
  console.log('已连接到数据库');
});

// 检查orders表是否存在
db.get("SELECT name FROM sqlite_master WHERE type='table' AND name='orders'", (err, row) => {
  if (err) {
    console.error('检查表失败:', err.message);
    db.close();
    process.exit(1);
  }

  if (!row) {
    console.log('orders表不存在，请先创建基础表结构');
    db.close();
    process.exit(1);
  }

  // 检查表结构
  db.all("PRAGMA table_info(orders)", (err, columns) => {
    if (err) {
      console.error('获取表结构失败:', err.message);
      db.close();
      process.exit(1);
    }

    // 检查是否已有新字段
    const hasDiscountAmount = columns.some(col => col.name === 'discount_amount');
    const hasPayableAmount = columns.some(col => col.name === 'payable_amount');
    const hasReceivedAmount = columns.some(col => col.name === 'received_amount');
    const hasChangeAmount = columns.some(col => col.name === 'change_amount');

    // 添加缺失的字段
    const alterTableQueries = [];

    if (!hasDiscountAmount) {
      alterTableQueries.push("ALTER TABLE orders ADD COLUMN discount_amount REAL DEFAULT 0");
    }

    if (!hasPayableAmount) {
      alterTableQueries.push("ALTER TABLE orders ADD COLUMN payable_amount REAL");
    }

    if (!hasReceivedAmount) {
      alterTableQueries.push("ALTER TABLE orders ADD COLUMN received_amount REAL");
    }

    if (!hasChangeAmount) {
      alterTableQueries.push("ALTER TABLE orders ADD COLUMN change_amount REAL DEFAULT 0");
    }

    // 执行所有ALTER TABLE语句
    if (alterTableQueries.length > 0) {
      console.log(`需要添加 ${alterTableQueries.length} 个字段到orders表`);
      
      // 使用事务执行所有更改
      db.serialize(() => {
        db.run("BEGIN TRANSACTION");

        alterTableQueries.forEach(query => {
          db.run(query, (err) => {
            if (err) {
              console.error(`执行 ${query} 失败:`, err.message);
              db.run("ROLLBACK");
              db.close();
              process.exit(1);
            }
            console.log(`成功执行: ${query}`);
          });
        });

        // 更新已有订单的payable_amount字段
        db.run(`
          UPDATE orders 
          SET payable_amount = total_amount - discount_amount,
              received_amount = total_amount
          WHERE payable_amount IS NULL
        `, (err) => {
          if (err) {
            console.error('更新已有订单数据失败:', err.message);
            db.run("ROLLBACK");
            db.close();
            process.exit(1);
          }
          console.log('已更新现有订单的支付信息');
        });

        db.run("COMMIT", (err) => {
          if (err) {
            console.error('提交事务失败:', err.message);
            db.close();
            process.exit(1);
          }
          console.log('数据库迁移成功完成');
          db.close();
        });
      });
    } else {
      console.log('所有必要字段已存在，无需迁移');
      db.close();
    }
  });
});
