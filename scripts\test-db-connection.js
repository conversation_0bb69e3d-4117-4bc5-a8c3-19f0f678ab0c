const sqlite3 = require('sqlite3').verbose();
const { open } = require('sqlite');
const path = require('path');

async function testConnection() {
  try {
    console.log('测试数据库连接...');
    
    const dbPath = path.resolve(process.cwd(), 'database.sqlite');
    console.log(`数据库路径: ${dbPath}`);
    
    const db = await open({
      filename: dbPath,
      driver: sqlite3.Database
    });
    
    console.log('数据库连接成功');
    
    // 查询所有表
    const tables = await db.all("SELECT name FROM sqlite_master WHERE type='table'");
    console.log('数据库中的表:');
    tables.forEach(table => {
      console.log(`  - ${table.name}`);
    });
    
    // 尝试查询products表
    try {
      const products = await db.all("SELECT * FROM products LIMIT 5");
      console.log('products表中的数据:');
      console.log(products);
    } catch (err) {
      console.error('查询products表失败:', err.message);
    }
    
    // 关闭数据库连接
    await db.close();
    console.log('数据库连接已关闭');
  } catch (err) {
    console.error('测试失败:', err);
  }
}

testConnection();
