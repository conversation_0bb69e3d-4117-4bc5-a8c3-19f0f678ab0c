const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

// 数据库文件路径
const dbPath = path.resolve(process.cwd(), 'database.sqlite');

console.log(`检查并修复数据库数据: ${dbPath}`);

// 检查数据库文件是否存在
if (!fs.existsSync(dbPath)) {
  console.log('数据库文件不存在');
  process.exit(1);
}

// 创建数据库连接
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('连接数据库失败:', err.message);
    process.exit(1);
  }
  console.log('已连接到数据库');
});

// 启用外键约束
db.run('PRAGMA foreign_keys = ON');

// 检查并修复categories表数据
function fixCategoriesData() {
  return new Promise((resolve, reject) => {
    db.get("SELECT COUNT(*) as count FROM categories", (err, row) => {
      if (err) {
        console.error('查询categories表失败:', err.message);
        return reject(err);
      }
      
      if (row && row.count > 0) {
        console.log(`categories表中已有${row.count}条数据，无需修复`);
        return resolve();
      }
      
      console.log('categories表中没有数据，开始插入...');
      
      // 插入药品分类数据
      const categories = [
        { name: '中成药', description: '传统中成药制剂' },
        { name: '化学药品', description: '西药、化学药品' },
        { name: '中药材', description: '中药材、饮片' },
        { name: '生物制品', description: '疫苗、血液制品等' },
        { name: '医疗器械', description: '医疗器械、耗材' },
        { name: '保健品', description: '保健食品、功能性食品' },
        { name: '感冒用药', description: '治疗感冒相关症状' },
        { name: '解热镇痛', description: '缓解疼痛和发热' },
        { name: '抗生素', description: '抗菌药物' },
        { name: '维生素类', description: '补充维生素' },
        { name: '清热解毒', description: '清热解毒类中药' }
      ];
      
      // 使用事务进行批量插入
      db.serialize(() => {
        db.run('BEGIN TRANSACTION');
        
        const stmt = db.prepare('INSERT INTO categories (name, description) VALUES (?, ?)');
        
        categories.forEach(category => {
          stmt.run(category.name, category.description, (err) => {
            if (err) {
              console.error(`插入分类 "${category.name}" 失败:`, err.message);
            }
          });
        });
        
        stmt.finalize();
        
        db.run('COMMIT', (err) => {
          if (err) {
            console.error('提交事务失败:', err.message);
            return reject(err);
          } else {
            console.log(`成功插入 ${categories.length} 条分类数据`);
            return resolve();
          }
        });
      });
    });
  });
}

// 检查并修复settings表数据
function fixSettingsData() {
  return new Promise((resolve, reject) => {
    db.get("SELECT COUNT(*) as count FROM settings", (err, row) => {
      if (err) {
        console.error('查询settings表失败:', err.message);
        return reject(err);
      }
      
      if (row && row.count > 0) {
        console.log(`settings表中已有${row.count}条数据，检查是否需要补充...`);
        
        // 检查是否有码上放心开放平台设置
        db.get("SELECT COUNT(*) as count FROM settings WHERE setting_name = 'mashangfangxinAppkey'", (err, row) => {
          if (err) {
            console.error('查询码上放心设置失败:', err.message);
            return reject(err);
          }
          
          if (row && row.count > 0) {
            console.log('码上放心开放平台设置已存在，无需添加');
            return resolve();
          }
          
          console.log('添加码上放心开放平台设置...');
          
          // 添加码上放心开放平台设置
          db.run('BEGIN TRANSACTION');
          
          db.run(
            'INSERT INTO settings (setting_name, setting_value, description) VALUES (?, ?, ?)',
            ['mashangfangxinAppkey', '', '码上放心开放平台AppKey'],
            (err) => {
              if (err) console.error('添加mashangfangxinAppkey失败:', err.message);
            }
          );
          
          db.run(
            'INSERT INTO settings (setting_name, setting_value, description) VALUES (?, ?, ?)',
            ['mashangfangxinAppsecret', '', '码上放心开放平台AppSecret'],
            (err) => {
              if (err) console.error('添加mashangfangxinAppsecret失败:', err.message);
            }
          );
          
          db.run(
            'INSERT INTO settings (setting_name, setting_value, description) VALUES (?, ?, ?)',
            ['mashangfangxinUrl', 'http://gw.api.taobao.com/router/rest', '码上放心开放平台API URL'],
            (err) => {
              if (err) console.error('添加mashangfangxinUrl失败:', err.message);
            }
          );
          
          db.run(
            'INSERT INTO settings (setting_name, setting_value, description) VALUES (?, ?, ?)',
            ['mashangfangxinRefEntId', '', '码上放心开放平台企业ID'],
            (err) => {
              if (err) console.error('添加mashangfangxinRefEntId失败:', err.message);
            }
          );
          
          db.run('COMMIT', (err) => {
            if (err) {
              console.error('提交事务失败:', err.message);
              return reject(err);
            } else {
              console.log('码上放心开放平台设置添加成功');
              return resolve();
            }
          });
        });
      } else {
        console.log('settings表中没有数据，添加基本设置...');
        
        // 添加基本设置
        db.run('BEGIN TRANSACTION');
        
        db.run(
          'INSERT INTO settings (setting_name, setting_value, description) VALUES (?, ?, ?)',
          ['storeName', '药店零售管理系统', '药店名称'],
          (err) => {
            if (err) console.error('添加storeName失败:', err.message);
          }
        );
        
        db.run(
          'INSERT INTO settings (setting_name, setting_value, description) VALUES (?, ?, ?)',
          ['orderNumberDigits', '4', '订单编号位数'],
          (err) => {
            if (err) console.error('添加orderNumberDigits失败:', err.message);
          }
        );
        
        // 添加码上放心开放平台设置
        db.run(
          'INSERT INTO settings (setting_name, setting_value, description) VALUES (?, ?, ?)',
          ['mashangfangxinAppkey', '', '码上放心开放平台AppKey'],
          (err) => {
            if (err) console.error('添加mashangfangxinAppkey失败:', err.message);
          }
        );
        
        db.run(
          'INSERT INTO settings (setting_name, setting_value, description) VALUES (?, ?, ?)',
          ['mashangfangxinAppsecret', '', '码上放心开放平台AppSecret'],
          (err) => {
            if (err) console.error('添加mashangfangxinAppsecret失败:', err.message);
          }
        );
        
        db.run(
          'INSERT INTO settings (setting_name, setting_value, description) VALUES (?, ?, ?)',
          ['mashangfangxinUrl', 'http://gw.api.taobao.com/router/rest', '码上放心开放平台API URL'],
          (err) => {
            if (err) console.error('添加mashangfangxinUrl失败:', err.message);
          }
        );
        
        db.run(
          'INSERT INTO settings (setting_name, setting_value, description) VALUES (?, ?, ?)',
          ['mashangfangxinRefEntId', '', '码上放心开放平台企业ID'],
          (err) => {
            if (err) console.error('添加mashangfangxinRefEntId失败:', err.message);
          }
        );
        
        db.run('COMMIT', (err) => {
          if (err) {
            console.error('提交事务失败:', err.message);
            return reject(err);
          } else {
            console.log('基本设置添加成功');
            return resolve();
          }
        });
      }
    });
  });
}

// 执行所有修复操作
async function fixAllData() {
  try {
    await fixCategoriesData();
    await fixSettingsData();
    
    console.log('所有数据修复完成');
  } catch (error) {
    console.error('数据修复过程中出错:', error);
  } finally {
    // 关闭数据库连接
    db.close((err) => {
      if (err) {
        console.error('关闭数据库失败:', err.message);
        process.exit(1);
      }
      console.log('数据库连接已关闭');
    });
  }
}

// 执行修复
fixAllData();
