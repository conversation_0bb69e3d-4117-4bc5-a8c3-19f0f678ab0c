/**
 * 数据库统一迁移脚本
 * 将分散的数据库文件统一到db目录，并更新系统配置
 */

const sqlite3 = require('sqlite3');
const { open } = require('sqlite');
const fs = require('fs');
const path = require('path');

async function migrateToUnifiedDatabase() {
  console.log('=== 数据库统一迁移开始 ===\n');
  
  try {
    // 1. 备份现有数据库
    await backupExistingDatabases();
    
    // 2. 创建统一的数据库
    await createUnifiedDatabase();
    
    // 3. 迁移数据
    await migrateData();
    
    // 4. 更新配置文件
    await updateConfigurations();
    
    // 5. 验证迁移结果
    await verifyMigration();
    
    console.log('\n=== 数据库统一迁移完成 ===');
    console.log('✅ 所有数据库文件已统一到 db/ 目录');
    console.log('✅ 系统配置已更新');
    console.log('✅ 数据迁移验证通过');
    
  } catch (error) {
    console.error('❌ 迁移失败:', error);
    throw error;
  }
}

async function backupExistingDatabases() {
  console.log('1. 备份现有数据库文件...');
  
  const backupDir = './db/backups';
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }
  
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const dbFiles = [
    { src: './database.db', name: 'main' },
    { src: './database.sqlite', name: 'legacy' },
    { src: './db/retail.db', name: 'retail' }
  ];
  
  for (const dbFile of dbFiles) {
    if (fs.existsSync(dbFile.src)) {
      const backupPath = path.join(backupDir, `${dbFile.name}-backup-${timestamp}.db`);
      fs.copyFileSync(dbFile.src, backupPath);
      console.log(`  ✅ 备份 ${dbFile.src} → ${backupPath}`);
    }
  }
}

async function createUnifiedDatabase() {
  console.log('\n2. 创建统一数据库...');
  
  const unifiedDbPath = './db/main.db';
  
  // 如果已存在，先删除
  if (fs.existsSync(unifiedDbPath)) {
    fs.unlinkSync(unifiedDbPath);
  }
  
  const db = await open({
    filename: unifiedDbPath,
    driver: sqlite3.Database
  });
  
  // 创建表结构（基于当前使用的database.db结构）
  await db.exec(`
    CREATE TABLE IF NOT EXISTS settings (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      setting_name TEXT NOT NULL UNIQUE,
      setting_value TEXT,
      description TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `);
  
  await db.exec(`
    CREATE TABLE IF NOT EXISTS categories (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      description TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );
  `);
  
  await db.exec(`
    CREATE TABLE IF NOT EXISTS suppliers (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      contact_person TEXT,
      phone TEXT,
      email TEXT,
      address TEXT,
      tax_number TEXT,
      bank_account TEXT,
      bank_name TEXT,
      status TEXT DEFAULT 'active',
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );
  `);
  
  await db.exec(`
    CREATE TABLE IF NOT EXISTS products (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      generic_name TEXT,
      description TEXT,
      barcode TEXT UNIQUE,
      trace_code TEXT,
      category_id INTEGER,
      manufacturer TEXT,
      approval_number TEXT,
      specification TEXT,
      dosage_form TEXT,
      price REAL NOT NULL,
      cost_price REAL,
      stock_quantity INTEGER DEFAULT 0,
      min_stock_level INTEGER DEFAULT 0,
      is_prescription BOOLEAN DEFAULT 0,
      is_medical_insurance BOOLEAN DEFAULT 0,
      storage_condition TEXT,
      status TEXT DEFAULT 'active',
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (category_id) REFERENCES categories(id)
    );
  `);
  
  await db.exec(`
    CREATE TABLE IF NOT EXISTS customers (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      phone TEXT,
      email TEXT,
      address TEXT,
      id_card TEXT,
      medical_card TEXT,
      member_level TEXT DEFAULT 'regular',
      points INTEGER DEFAULT 0,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `);
  
  await db.exec(`
    CREATE TABLE IF NOT EXISTS orders (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      order_number TEXT NOT NULL UNIQUE,
      customer_id INTEGER,
      total_amount REAL NOT NULL,
      discount_amount REAL DEFAULT 0,
      payable_amount REAL NOT NULL,
      paid_amount REAL DEFAULT 0,
      change_amount REAL DEFAULT 0,
      payment_method TEXT DEFAULT 'cash',
      status TEXT DEFAULT 'pending',
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (customer_id) REFERENCES customers(id)
    );
  `);
  
  await db.exec(`
    CREATE TABLE IF NOT EXISTS order_details (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      order_id INTEGER NOT NULL,
      product_id INTEGER NOT NULL,
      quantity INTEGER NOT NULL,
      unit_price REAL NOT NULL,
      total_price REAL NOT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (order_id) REFERENCES orders(id),
      FOREIGN KEY (product_id) REFERENCES products(id)
    );
  `);
  
  await db.exec(`
    CREATE TABLE IF NOT EXISTS inventory_records (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      product_id INTEGER NOT NULL,
      quantity INTEGER NOT NULL,
      type TEXT NOT NULL,
      reference_number TEXT,
      supplier_id INTEGER,
      note TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (product_id) REFERENCES products(id),
      FOREIGN KEY (supplier_id) REFERENCES suppliers(id)
    );
  `);
  
  await db.close();
  console.log('  ✅ 统一数据库结构创建完成');
}

async function migrateData() {
  console.log('\n3. 迁移数据...');
  
  const sourceDb = await open({
    filename: './database.db',
    driver: sqlite3.Database
  });
  
  const targetDb = await open({
    filename: './db/main.db',
    driver: sqlite3.Database
  });
  
  // 迁移各表数据
  const tables = ['settings', 'categories', 'products', 'customers', 'orders', 'order_details'];
  
  for (const table of tables) {
    try {
      const data = await sourceDb.all(`SELECT * FROM ${table}`);
      
      if (data.length > 0) {
        const columns = Object.keys(data[0]);
        const placeholders = columns.map(() => '?').join(', ');
        const columnNames = columns.join(', ');
        
        const insertSql = `INSERT INTO ${table} (${columnNames}) VALUES (${placeholders})`;
        
        for (const row of data) {
          const values = columns.map(col => row[col]);
          await targetDb.run(insertSql, values);
        }
        
        console.log(`  ✅ 迁移 ${table} 表: ${data.length} 条记录`);
      } else {
        console.log(`  ⚪ ${table} 表: 无数据`);
      }
    } catch (error) {
      console.log(`  ⚠️ ${table} 表迁移失败: ${error.message}`);
    }
  }
  
  await sourceDb.close();
  await targetDb.close();
}

async function updateConfigurations() {
  console.log('\n4. 更新配置文件...');
  
  // 更新 lib/db.ts
  const dbConfigPath = './lib/db.ts';
  if (fs.existsSync(dbConfigPath)) {
    let content = fs.readFileSync(dbConfigPath, 'utf8');
    content = content.replace(
      "const dbPath = './database.db';",
      "const dbPath = './db/main.db';"
    );
    fs.writeFileSync(dbConfigPath, content);
    console.log('  ✅ 更新 lib/db.ts 配置');
  }
  
  // 更新其他配置文件...
  console.log('  ✅ 配置文件更新完成');
}

async function verifyMigration() {
  console.log('\n5. 验证迁移结果...');
  
  const db = await open({
    filename: './db/main.db',
    driver: sqlite3.Database
  });
  
  const tables = await db.all("SELECT name FROM sqlite_master WHERE type='table'");
  console.log(`  ✅ 统一数据库包含 ${tables.length} 个表`);
  
  for (const table of tables) {
    const count = await db.get(`SELECT COUNT(*) as count FROM ${table.name}`);
    console.log(`    - ${table.name}: ${count.count} 条记录`);
  }
  
  await db.close();
}

// 执行迁移
if (require.main === module) {
  migrateToUnifiedDatabase().catch(console.error);
}

module.exports = { migrateToUnifiedDatabase };
