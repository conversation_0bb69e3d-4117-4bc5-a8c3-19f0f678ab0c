const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

// 数据库文件路径
const dbPath = path.resolve(process.cwd(), 'database.sqlite');

console.log(`检查并修复inventory_records表: ${dbPath}`);

// 检查数据库文件是否存在
if (!fs.existsSync(dbPath)) {
  console.log('数据库文件不存在');
  process.exit(1);
}

// 创建数据库连接
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('连接数据库失败:', err.message);
    process.exit(1);
  }
  console.log('已连接到数据库');
});

// 检查inventory_records表是否存在
db.get("SELECT name FROM sqlite_master WHERE type='table' AND name='inventory_records'", (err, row) => {
  if (err) {
    console.error('查询表失败:', err.message);
    db.close();
    process.exit(1);
  }

  if (!row) {
    console.log('inventory_records表不存在，无需修复');
    db.close();
    return;
  }

  // 检查表结构，查看是否有batch_id列
  db.all("PRAGMA table_info(inventory_records)", (err, columns) => {
    if (err) {
      console.error('获取表结构失败:', err.message);
      db.close();
      process.exit(1);
    }

    // 查找batch_id列
    const batchIdColumn = columns.find(col => col.name === 'batch_id');

    if (batchIdColumn) {
      console.log('inventory_records表已有batch_id列，无需修复');
      db.close();
      return;
    }

    console.log('inventory_records表缺少batch_id列，开始修复...');

    // 开始事务
    db.run('BEGIN TRANSACTION', (err) => {
      if (err) {
        console.error('开始事务失败:', err.message);
        db.close();
        process.exit(1);
      }

      // 创建临时表
      db.run(`
        CREATE TABLE inventory_records_temp (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          product_id INTEGER NOT NULL,
          batch_id INTEGER,
          quantity_change INTEGER NOT NULL,
          change_type TEXT NOT NULL,
          reference_id INTEGER,
          reference_type TEXT,
          note TEXT,
          operator_id INTEGER,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (product_id) REFERENCES products(id),
          FOREIGN KEY (batch_id) REFERENCES product_batches(id)
        )
      `, (err) => {
        if (err) {
          console.error('创建临时表失败:', err.message);
          db.run('ROLLBACK');
          db.close();
          process.exit(1);
        }

        // 获取原表的列名
        db.all("PRAGMA table_info(inventory_records)", (err, columns) => {
          if (err) {
            console.error('获取表结构失败:', err.message);
            db.run('ROLLBACK');
            db.close();
            process.exit(1);
          }

          // 获取原表的所有列名
          const columnNames = columns
            .filter(col => col.name !== 'id')
            .map(col => col.name);

          // 获取临时表的所有列名
          db.all("PRAGMA table_info(inventory_records_temp)", (err, tempColumns) => {
            if (err) {
              console.error('获取临时表结构失败:', err.message);
              db.run('ROLLBACK');
              db.close();
              process.exit(1);
            }

            // 找出两个表共有的列
            const tempColumnNames = tempColumns
              .filter(col => col.name !== 'id')
              .map(col => col.name);

            // 找出两个表共有的列
            const commonColumns = columnNames.filter(col =>
              tempColumnNames.includes(col) || col === 'product_id' ||
              col === 'quantity_change' || col === 'change_type'
            );

            console.log('共有列:', commonColumns.join(', '));

            if (commonColumns.length === 0) {
              console.error('没有找到共有列，无法复制数据');
              db.run('ROLLBACK');
              db.close();
              process.exit(1);
            }

            const columnsStr = commonColumns.join(', ');

            // 复制数据到临时表
            db.run(`
              INSERT INTO inventory_records_temp (${columnsStr}, batch_id)
              SELECT ${columnsStr}, NULL FROM inventory_records
            `, (err) => {
            if (err) {
              console.error('复制数据失败:', err.message);
              db.run('ROLLBACK');
              db.close();
              process.exit(1);
            }

            // 删除原表
            db.run('DROP TABLE inventory_records', (err) => {
              if (err) {
                console.error('删除原表失败:', err.message);
                db.run('ROLLBACK');
                db.close();
                process.exit(1);
              }

              // 重命名临时表
              db.run('ALTER TABLE inventory_records_temp RENAME TO inventory_records', (err) => {
                if (err) {
                  console.error('重命名表失败:', err.message);
                  db.run('ROLLBACK');
                  db.close();
                  process.exit(1);
                }

                // 提交事务
                db.run('COMMIT', (err) => {
                  if (err) {
                    console.error('提交事务失败:', err.message);
                    db.run('ROLLBACK');
                    db.close();
                    process.exit(1);
                  }

                  console.log('inventory_records表修复完成，已添加batch_id列');
                  db.close();
                });
              });
            });
          });
        });
      });
    });
  });
});
