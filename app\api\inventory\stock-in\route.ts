import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';

interface StockInRequest {
  product_id: number;
  quantity: number;
  batch_number?: string;
  supplier_id?: number;
  purchase_price?: number;
  production_date?: string;
  expiry_date?: string;
  stock_in_date: string;
  notes?: string;
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json() as StockInRequest;
    const { 
      product_id, 
      quantity, 
      batch_number, 
      supplier_id, 
      purchase_price, 
      production_date, 
      expiry_date, 
      stock_in_date, 
      notes
    } = body;
    
    // 验证数据
    if (!product_id || !quantity || !stock_in_date) {
      return NextResponse.json({ 
        success: false, 
        message: '缺少必要参数' 
      }, { status: 400 });
    }

    // 1. 获取产品信息
    const product = await db.product.findUnique({
      where: { id: product_id }
    });

    if (!product) {
      return NextResponse.json({ 
        success: false, 
        message: '未找到产品' 
      }, { status: 404 });
    }

    // 2. 创建入库记录
    const stockIn = await db.stockIn.create({
      data: {
        product_id,
        quantity,
        batch_number: batch_number || '',
        supplier_id: supplier_id || null,
        purchase_price: purchase_price || 0,
        production_date: production_date ? new Date(production_date) : null,
        expiry_date: expiry_date ? new Date(expiry_date) : null,
        stock_in_date: new Date(stock_in_date),
        notes: notes || '',
      }
    });

    // 3. 更新产品库存
    const updatedProduct = await db.product.update({
      where: { id: product_id },
      data: {
        stock_quantity: {
          increment: quantity
        }
      }
    });

    // 4. 如果有批次信息，创建或更新批次
    if (batch_number) {
      // 查找是否已存在相同批次
      const existingBatch = await db.batch.findFirst({
        where: {
          product_id,
          batch_number
        }
      });

      if (existingBatch) {
        // 更新现有批次
        await db.batch.update({
          where: { id: existingBatch.id },
          data: {
            quantity: {
              increment: quantity
            },
            status: 'active',
            production_date: production_date ? new Date(production_date) : existingBatch.production_date,
            expiry_date: expiry_date ? new Date(expiry_date) : existingBatch.expiry_date,
          }
        });
      } else {
        // 创建新批次
        await db.batch.create({
          data: {
            product_id,
            batch_number,
            quantity,
            supplier_id: supplier_id || null,
            purchase_price: purchase_price || 0,
            production_date: production_date ? new Date(production_date) : null,
            expiry_date: expiry_date ? new Date(expiry_date) : null,
            status: 'active'
          }
        });
      }
    }

    return NextResponse.json({ 
      success: true, 
      data: {
        stock_in: stockIn,
        product: updatedProduct
      }
    });
  } catch (error) {
    console.error('入库处理错误:', error);
    return NextResponse.json({ 
      success: false, 
      message: '服务器处理入库请求时出错' 
    }, { status: 500 });
  }
} 