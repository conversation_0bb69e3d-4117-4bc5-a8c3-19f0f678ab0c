/**
 * 添加码上放心开放平台设置项的数据库迁移脚本
 * 
 * 运行方式：
 * node scripts/add-mashangfangxin-settings.js
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

// 数据库文件路径
const dbPath = path.resolve(process.cwd(), 'database.sqlite');

console.log(`正在更新数据库: ${dbPath}`);

// 检查数据库文件是否存在
if (!fs.existsSync(dbPath)) {
  console.error('数据库文件不存在，请先初始化数据库');
  process.exit(1);
}

// 创建数据库连接
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('连接数据库失败:', err.message);
    process.exit(1);
  }
  console.log('已连接到数据库');
});

// 启用外键约束
db.run('PRAGMA foreign_keys = ON');

// 检查settings表是否存在
db.get("SELECT name FROM sqlite_master WHERE type='table' AND name='settings'", (err, row) => {
  if (err) {
    console.error('检查表失败:', err.message);
    db.close();
    process.exit(1);
  }

  if (!row) {
    console.log('settings表不存在，创建表...');
    createSettingsTable();
  } else {
    console.log('settings表已存在，添加码上放心开放平台设置项...');
    addMashangfangxinSettings();
  }
});

// 创建settings表
function createSettingsTable() {
  db.run(`
    CREATE TABLE IF NOT EXISTS settings (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      setting_name TEXT NOT NULL UNIQUE,
      setting_value TEXT,
      description TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
  `, (err) => {
    if (err) {
      console.error('创建settings表失败:', err.message);
      db.close();
      process.exit(1);
    }
    console.log('settings表创建成功');
    addMashangfangxinSettings();
  });
}

// 添加码上放心开放平台设置项
function addMashangfangxinSettings() {
  // 检查是否已存在码上放心开放平台设置项
  db.get("SELECT COUNT(*) as count FROM settings WHERE setting_name = 'mashangfangxinAppkey'", (err, row) => {
    if (err) {
      console.error('检查设置项失败:', err.message);
      db.close();
      process.exit(1);
    }

    if (row && row.count > 0) {
      console.log('码上放心开放平台设置项已存在，跳过添加');
      addBasicSettings();
    } else {
      console.log('添加码上放心开放平台设置项...');
      
      // 添加码上放心开放平台设置项
      db.serialize(() => {
        const stmt = db.prepare(`
          INSERT INTO settings (setting_name, setting_value, description)
          VALUES (?, ?, ?)
        `);
        
        stmt.run('mashangfangxinAppkey', '', '码上放心开放平台AppKey');
        stmt.run('mashangfangxinAppsecret', '', '码上放心开放平台AppSecret');
        stmt.run('mashangfangxinUrl', 'http://gw.api.taobao.com/router/rest', '码上放心开放平台API URL');
        
        stmt.finalize((err) => {
          if (err) {
            console.error('添加码上放心开放平台设置项失败:', err.message);
          } else {
            console.log('码上放心开放平台设置项添加成功');
          }
          addBasicSettings();
        });
      });
    }
  });
}

// 添加基本设置项（如果不存在）
function addBasicSettings() {
  // 检查是否已存在基本设置项
  db.get("SELECT COUNT(*) as count FROM settings WHERE setting_name = 'storeName'", (err, row) => {
    if (err) {
      console.error('检查设置项失败:', err.message);
      db.close();
      process.exit(1);
    }

    if (row && row.count > 0) {
      console.log('基本设置项已存在，跳过添加');
      closeDatabase();
    } else {
      console.log('添加基本设置项...');
      
      // 添加基本设置项
      db.serialize(() => {
        const stmt = db.prepare(`
          INSERT INTO settings (setting_name, setting_value, description)
          VALUES (?, ?, ?)
        `);
        
        stmt.run('storeName', '药店零售管理系统', '药店名称');
        stmt.run('orderNumberDigits', '4', '订单编号位数');
        
        stmt.finalize((err) => {
          if (err) {
            console.error('添加基本设置项失败:', err.message);
          } else {
            console.log('基本设置项添加成功');
          }
          closeDatabase();
        });
      });
    }
  });
}

// 关闭数据库连接
function closeDatabase() {
  db.close((err) => {
    if (err) {
      console.error('关闭数据库失败:', err.message);
      process.exit(1);
    }
    console.log('数据库更新完成');
  });
}
