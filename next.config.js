/** @type {import('next').NextConfig} */
const nextConfig = {
  // 启用实验性的服务器组件功能
  experimental: {
    serverComponentsExternalPackages: ['sqlite3', 'sqlite'],
  },
  // 配置webpack，添加对Node.js模块的支持
  webpack: (config, { isServer }) => {
    // 如果是客户端构建，为Node.js模块提供polyfill
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: require.resolve('path-browserify'),
        stream: require.resolve('stream-browserify'),
        crypto: require.resolve('crypto-browserify'),
        http: require.resolve('stream-http'),
        https: require.resolve('https-browserify'),
        zlib: require.resolve('browserify-zlib'),
        assert: require.resolve('assert/'),
        url: require.resolve('url/'),
        os: require.resolve('os-browserify/browser'),
        buffer: require.resolve('buffer/'),
        util: require.resolve('util/'),
        'string_decoder': require.resolve('string_decoder/'),
      };
    }
    
    return config;
  },
  // 禁用严格模式，以避免某些第三方库的兼容性问题
  reactStrictMode: false,
};

module.exports = nextConfig;
