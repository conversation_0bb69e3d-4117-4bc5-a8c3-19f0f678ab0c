# 药店零售管理系统

这是一个基于[Next.js](https://nextjs.org)开发的药店零售管理系统，使用[`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app)创建。

## 开始使用

首先，运行开发服务器：

```bash
npm run dev
# 或
yarn dev
# 或
pnpm dev
# 或
bun dev
```

使用浏览器打开[http://localhost:3000](http://localhost:3000)查看结果。

您可以通过修改`app/page.tsx`开始编辑页面。当您编辑文件时，页面会自动更新。

## 系统功能

本系统主要包含以下功能模块：

- **药品管理**：添加、编辑、删除药品信息，包含处方药和医保药品标识，支持扫码枪扫描药品追溯码获取药品信息
- **库存管理**：药品入库、出库记录，库存预警
- **销售管理**：日常销售、处方销售记录
- **供应商管理**：供应商信息维护
- **用户权限管理**：系统用户管理和权限控制

## 技术栈

本项目使用以下技术：

- **Next.js**：React框架
- **Tailwind CSS**：样式框架
- **SQLite**：本地数据库
- **TypeScript**：类型检查

## 数据库结构

系统使用SQLite数据库，主要表结构包括：

- 药品表(药品)
- 药品分类表(药品分类)
- 供应商表(供应商)
- 库存变动记录表(库存变动记录)
- 药品批次表(药品批次)
- 用户表(用户)
- 销售订单表(订单)

## 药品追溯码功能

系统集成了码上放心开放平台的药品追溯码功能，支持：

1. 使用扫码枪扫描药品追溯码或商品条码
2. 自动识别条码类型（药品追溯码或商品条码）
3. 通过药品追溯码获取药品详细信息
4. 将扫描的药品信息快速添加到药品库

### 配置码上放心开放平台

1. 在码上放心开放平台注册并创建应用
2. 获取应用的AppKey和AppSecret
3. 复制`.env.local.example`文件为`.env.local`，并填入相应的AppKey和AppSecret

## 部署

推荐使用[Vercel平台](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme)部署本Next.js应用。

更多部署详情，请查看[Next.js部署文档](https://nextjs.org/docs/app/building-your-application/deploying)。
