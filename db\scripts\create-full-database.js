// 创建完整的数据库结构和测试数据
const Database = require('better-sqlite3');
const path = require('path');

// 数据库文件路径
const dbPath = path.resolve(process.cwd(), 'database.db');

console.log(`创建完整数据库: ${dbPath}`);

// 创建数据库连接
const db = new Database(dbPath);

console.log('已连接到数据库');

// 启用外键约束
db.pragma('foreign_keys = ON');

// 创建表结构
console.log('开始创建表结构...');

try {
  // 1. 创建设置表
  db.exec(`
    CREATE TABLE IF NOT EXISTS settings (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      setting_name TEXT NOT NULL UNIQUE,
      setting_value TEXT,
      description TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
  `);
  console.log('✓ settings表创建成功');

  // 2. 创建分类表
  db.exec(`
    CREATE TABLE IF NOT EXISTS categories (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      description TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
  `);
  console.log('✓ categories表创建成功');

  // 3. 创建产品表
  db.exec(`
    CREATE TABLE IF NOT EXISTS products (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      generic_name TEXT,
      specification TEXT,
      manufacturer TEXT,
      category_id INTEGER,
      stock_quantity INTEGER DEFAULT 0,
      price REAL NOT NULL,
      cost_price REAL,
      barcode TEXT,
      storage_condition TEXT,
      status TEXT DEFAULT 'active',
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (category_id) REFERENCES categories(id)
    )
  `);
  console.log('✓ products表创建成功');

  // 4. 创建客户表
  db.exec(`
    CREATE TABLE IF NOT EXISTS customers (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      phone TEXT,
      address TEXT,
      is_member INTEGER DEFAULT 0,
      member_number TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
  `);
  console.log('✓ customers表创建成功');

  // 5. 创建订单表
  db.exec(`
    CREATE TABLE IF NOT EXISTS orders (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      order_number TEXT NOT NULL UNIQUE,
      customer_id INTEGER,
      total_amount REAL NOT NULL,
      discount_amount REAL DEFAULT 0,
      payable_amount REAL NOT NULL,
      received_amount REAL,
      change_amount REAL DEFAULT 0,
      payment_method TEXT,
      status TEXT DEFAULT 'completed',
      note TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (customer_id) REFERENCES customers(id)
    )
  `);
  console.log('✓ orders表创建成功');

  // 6. 创建订单明细表
  db.exec(`
    CREATE TABLE IF NOT EXISTS order_details (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      order_id INTEGER NOT NULL,
      product_id INTEGER NOT NULL,
      quantity INTEGER NOT NULL,
      unit_price REAL NOT NULL,
      subtotal REAL NOT NULL,
      FOREIGN KEY (order_id) REFERENCES orders(id),
      FOREIGN KEY (product_id) REFERENCES products(id)
    )
  `);
  console.log('✓ order_details表创建成功');

  // 7. 创建库存记录表
  db.exec(`
    CREATE TABLE IF NOT EXISTS inventory_records (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      product_id INTEGER NOT NULL,
      type TEXT NOT NULL,
      quantity INTEGER NOT NULL,
      note TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (product_id) REFERENCES products(id)
    )
  `);
  console.log('✓ inventory_records表创建成功');

  // 8. 创建供应商表
  db.exec(`
    CREATE TABLE IF NOT EXISTS suppliers (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      contact_person TEXT,
      phone TEXT,
      address TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
  `);
  console.log('✓ suppliers表创建成功');

  console.log('表结构创建完成，开始插入测试数据...');

  // 插入默认设置
  const insertSettings = db.prepare(`
    INSERT OR IGNORE INTO settings (setting_name, setting_value, description) VALUES (?, ?, ?)
  `);

  const settings = [
    ['storeName', '药店零售管理系统', '药店名称'],
    ['orderNumberDigits', '4', '订单编号位数'],
    ['mashangfangxinAppkey', '', '码上放心开放平台AppKey'],
    ['mashangfangxinAppsecret', '', '码上放心开放平台AppSecret'],
    ['mashangfangxinUrl', 'http://gw.api.taobao.com/router/rest', '码上放心开放平台API URL'],
    ['mashangfangxinRefEntId', '24123445', '码上放心开放平台企业ID']
  ];

  for (const setting of settings) {
    insertSettings.run(setting);
  }
  console.log('✓ 默认设置插入成功');

  // 插入分类数据
  const insertCategory = db.prepare(`
    INSERT OR IGNORE INTO categories (id, name, description) VALUES (?, ?, ?)
  `);

  const categories = [
    [1, '抗生素', '抗菌药物'],
    [2, '解热镇痛', '缓解疼痛和发热'],
    [3, '感冒用药', '治疗感冒症状'],
    [4, '清热解毒', '清热解毒类中药'],
    [5, '维生素类', '补充维生素']
  ];

  for (const category of categories) {
    insertCategory.run(category);
  }
  console.log('✓ 分类数据插入成功');

  // 插入产品数据
  const insertProduct = db.prepare(`
    INSERT OR IGNORE INTO products (id, name, generic_name, specification, manufacturer, category_id, stock_quantity, price, cost_price, barcode, storage_condition) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `);

  const products = [
    [1, '阿莫西林胶囊', '阿莫西林胶囊', '0.25g*24粒/盒', '哈药集团制药总厂', 1, 120, 36.8, 28.5, '6901234567890', '阴凉干燥处'],
    [2, '布洛芬片', '布洛芬片', '0.1g*24片/盒', '上海信谊药厂有限公司', 2, 85, 18.5, 12.8, '6901234567891', '阴凉干燥处'],
    [3, '感冒灵颗粒', '感冒灵颗粒', '10g*9袋/盒', '哈药集团制药总厂', 3, 64, 25.6, 18.2, '6901234567892', '阴凉干燥处'],
    [4, '板蓝根颗粒', '板蓝根颗粒', '10g*20袋/盒', '广州白云山和记黄埔中药有限公司', 4, 76, 32.8, 24.5, '6901234567893', '阴凉干燥处'],
    [5, '维生素C片', '维生素C片', '0.1g*100片/瓶', '华北制药股份有限公司', 5, 180, 12.5, 8.6, '6901234567894', '阴凉干燥处']
  ];

  for (const product of products) {
    insertProduct.run(product);
  }
  console.log('✓ 产品数据插入成功');

  // 插入客户数据
  const insertCustomer = db.prepare(`
    INSERT OR IGNORE INTO customers (id, name, phone, is_member, member_number) VALUES (?, ?, ?, ?, ?)
  `);

  const customers = [
    [1, '测试客户', '13800138000', 1, 'VIP001'],
    [2, '张三', '13800138001', 0, ''],
    [3, '李四', '13800138002', 1, 'VIP002']
  ];

  for (const customer of customers) {
    insertCustomer.run(customer);
  }
  console.log('✓ 客户数据插入成功');

  // 插入测试订单
  const insertOrder = db.prepare(`
    INSERT OR IGNORE INTO orders (id, order_number, customer_id, total_amount, discount_amount, payable_amount, received_amount, change_amount, payment_method, status, note) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `);

  insertOrder.run([1, 'SO-202505170001', 1, 73.8, 5.0, 68.8, 70.0, 1.2, 'cash', 'completed', '测试订单']);
  console.log('✓ 订单数据插入成功');

  // 插入订单明细
  const insertOrderDetail = db.prepare(`
    INSERT OR IGNORE INTO order_details (id, order_id, product_id, quantity, unit_price, subtotal) VALUES (?, ?, ?, ?, ?, ?)
  `);

  const orderDetails = [
    [1, 1, 1, 1, 36.8, 36.8],
    [2, 1, 2, 2, 18.5, 37.0]
  ];

  for (const detail of orderDetails) {
    insertOrderDetail.run(detail);
  }
  console.log('✓ 订单明细插入成功');

  console.log('数据库创建和初始化完成！');

} catch (error) {
  console.error('数据库操作失败:', error);
} finally {
  // 关闭数据库连接
  db.close();
  console.log('数据库连接已关闭');
}
