const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

// 数据库文件路径
const dbPath = path.resolve(process.cwd(), 'database.sqlite');

console.log(`初始化数据库: ${dbPath}`);

// 检查数据库文件是否存在
if (fs.existsSync(dbPath)) {
  console.log('数据库文件已存在，将直接使用');
} else {
  console.log('数据库文件不存在，将创建新文件');
}

// 创建新的数据库连接
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('创建数据库失败:', err.message);
    process.exit(1);
  }
  console.log('已创建新的数据库文件');
});

// 启用外键约束
db.run('PRAGMA foreign_keys = ON');

// 检查表是否已存在
db.get("SELECT name FROM sqlite_master WHERE type='table' AND name='药品分类'", (err, row) => {
  if (err) {
    console.error('检查表失败:', err.message);
    db.close();
    process.exit(1);
  }

  if (row) {
    console.log('表已存在，跳过创建表步骤');
    db.close((err) => {
      if (err) {
        console.error('关闭数据库失败:', err.message);
        process.exit(1);
      }
      console.log('数据库初始化完成');
    });
    return;
  }

  console.log('开始创建表...');

  // 创建表
  db.serialize(() => {
    // 创建药品分类表
    db.run(`
      CREATE TABLE IF NOT EXISTS 药品分类 (
        编号 INTEGER PRIMARY KEY AUTOINCREMENT,
        名称 TEXT NOT NULL,
        描述 TEXT,
        创建时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

  // 创建药品表
  db.run(`
    CREATE TABLE IF NOT EXISTS 药品 (
      编号 INTEGER PRIMARY KEY AUTOINCREMENT,
      名称 TEXT NOT NULL,
      通用名 TEXT,
      规格 TEXT,
      生产厂家 TEXT,
      分类编号 INTEGER,
      库存数量 INTEGER DEFAULT 0,
      售价 REAL NOT NULL,
      进价 REAL,
      库存编码 TEXT,
      条形码 TEXT,
      状态 TEXT DEFAULT 'active',
      创建时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (分类编号) REFERENCES 药品分类(编号)
    )
  `);

  // 创建药品批次表
  db.run(`
    CREATE TABLE IF NOT EXISTS 药品批次 (
      编号 INTEGER PRIMARY KEY AUTOINCREMENT,
      药品编号 INTEGER NOT NULL,
      批号 TEXT,
      生产日期 DATE,
      有效期 DATE,
      数量 INTEGER DEFAULT 0,
      进价 REAL,
      创建时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (药品编号) REFERENCES 药品(编号)
    )
  `);

  // 创建客户表
  db.run(`
    CREATE TABLE IF NOT EXISTS 客户 (
      编号 INTEGER PRIMARY KEY AUTOINCREMENT,
      姓名 TEXT NOT NULL,
      电话 TEXT,
      地址 TEXT,
      备注 TEXT,
      创建时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // 创建供应商表
  db.run(`
    CREATE TABLE IF NOT EXISTS 供应商 (
      编号 INTEGER PRIMARY KEY AUTOINCREMENT,
      名称 TEXT NOT NULL,
      联系人 TEXT,
      电话 TEXT,
      地址 TEXT,
      备注 TEXT,
      创建时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // 创建用户表
  db.run(`
    CREATE TABLE IF NOT EXISTS 用户 (
      编号 INTEGER PRIMARY KEY AUTOINCREMENT,
      用户名 TEXT NOT NULL UNIQUE,
      密码 TEXT NOT NULL,
      姓名 TEXT,
      角色 TEXT DEFAULT 'user',
      状态 TEXT DEFAULT 'active',
      创建时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // 创建订单表
  db.run(`
    CREATE TABLE IF NOT EXISTS 订单 (
      编号 INTEGER PRIMARY KEY AUTOINCREMENT,
      订单编号 TEXT NOT NULL UNIQUE,
      客户编号 INTEGER,
      操作员编号 INTEGER,
      总金额 REAL NOT NULL,
      支付方式 TEXT,
      订单状态 TEXT DEFAULT '已完成',
      备注 TEXT,
      下单时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (客户编号) REFERENCES 客户(编号),
      FOREIGN KEY (操作员编号) REFERENCES 用户(编号)
    )
  `);

  // 创建订单明细表
  db.run(`
    CREATE TABLE IF NOT EXISTS 订单明细 (
      编号 INTEGER PRIMARY KEY AUTOINCREMENT,
      订单编号 INTEGER NOT NULL,
      药品编号 INTEGER NOT NULL,
      批次编号 INTEGER,
      数量 INTEGER NOT NULL,
      单价 REAL NOT NULL,
      小计 REAL NOT NULL,
      FOREIGN KEY (订单编号) REFERENCES 订单(编号),
      FOREIGN KEY (药品编号) REFERENCES 药品(编号),
      FOREIGN KEY (批次编号) REFERENCES 药品批次(编号)
    )
  `);

  // 创建库存变动记录表
  db.run(`
    CREATE TABLE IF NOT EXISTS 库存变动记录 (
      编号 INTEGER PRIMARY KEY AUTOINCREMENT,
      药品编号 INTEGER NOT NULL,
      批次编号 INTEGER,
      变动数量 INTEGER NOT NULL,
      变动类型 TEXT NOT NULL,
      关联单据编号 INTEGER,
      关联单据类型 TEXT,
      操作员编号 INTEGER,
      变动时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (药品编号) REFERENCES 药品(编号),
      FOREIGN KEY (批次编号) REFERENCES 药品批次(编号),
      FOREIGN KEY (操作员编号) REFERENCES 用户(编号)
    )
  `);

  // 创建系统设置表
  db.run(`
    CREATE TABLE IF NOT EXISTS 系统设置 (
      编号 INTEGER PRIMARY KEY AUTOINCREMENT,
      设置名称 TEXT NOT NULL UNIQUE,
      设置值 TEXT,
      描述 TEXT,
      更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // 检查是否已有用户数据
  db.get("SELECT COUNT(*) as count FROM 用户", (err, row) => {
    if (err) {
      console.error('检查用户数据失败:', err.message);
    } else if (row.count === 0) {
      console.log('插入默认用户数据...');
      // 插入默认用户
      db.run(`
        INSERT INTO 用户 (用户名, 密码, 姓名, 角色)
        VALUES ('admin', 'admin123', '管理员', 'admin')
      `);
    } else {
      console.log('用户数据已存在，跳过插入');
    }
  });

  // 检查是否已有系统设置数据
  db.get("SELECT COUNT(*) as count FROM 系统设置", (err, row) => {
    if (err) {
      console.error('检查系统设置数据失败:', err.message);
    } else if (row.count === 0) {
      console.log('插入默认系统设置数据...');
      // 插入默认系统设置
      db.run(`
        INSERT INTO 系统设置 (设置名称, 设置值, 描述)
        VALUES
        ('药店名称', '药店零售管理系统', '药店名称'),
        ('订单编号位数', '4', '订单编号日期后的序号位数')
      `);
    } else {
      console.log('系统设置数据已存在，跳过插入');
    }
  });

  // 检查是否已有药品分类数据
  db.get("SELECT COUNT(*) as count FROM 药品分类", (err, row) => {
    if (err) {
      console.error('检查药品分类数据失败:', err.message);
    } else if (row.count === 0) {
      console.log('插入示例药品分类数据...');
      // 插入示例药品分类
      db.run(`
        INSERT INTO 药品分类 (名称, 描述)
        VALUES
        ('抗生素', '抗菌药物'),
        ('解热镇痛', '缓解疼痛和发热'),
        ('感冒用药', '治疗感冒症状'),
        ('清热解毒', '清热解毒类中药'),
        ('维生素类', '补充维生素')
      `);
    } else {
      console.log('药品分类数据已存在，跳过插入');
    }
  });

  // 检查是否已有药品数据
  db.get("SELECT COUNT(*) as count FROM 药品", (err, row) => {
    if (err) {
      console.error('检查药品数据失败:', err.message);
    } else if (row.count === 0) {
      console.log('插入示例药品数据...');
      // 插入示例药品
      db.run(`
        INSERT INTO 药品 (名称, 通用名, 规格, 生产厂家, 分类编号, 库存数量, 售价, 进价, 库存编码)
        VALUES
        ('阿莫西林胶囊', '阿莫西林胶囊', '0.25g*24粒/盒', '哈药集团制药总厂', 1, 120, 36.8, 28.5, 'P001'),
        ('布洛芬片', '布洛芬片', '0.1g*24片/盒', '上海信谊药厂有限公司', 2, 85, 18.5, 12.8, 'P002'),
        ('感冒灵颗粒', '感冒灵颗粒', '10g*9袋/盒', '哈药集团制药总厂', 3, 64, 25.6, 18.2, 'P003'),
        ('板蓝根颗粒', '板蓝根颗粒', '10g*20袋/盒', '广州白云山和记黄埔中药有限公司', 4, 76, 32.8, 24.5, 'P004'),
        ('维生素C片', '维生素C片', '0.1g*100片/瓶', '华北制药股份有限公司', 5, 180, 12.5, 8.6, 'P005')
      `);
    } else {
      console.log('药品数据已存在，跳过插入');
    }
  });

  // 检查是否已有药品批次数据
  db.get("SELECT COUNT(*) as count FROM 药品批次", (err, row) => {
    if (err) {
      console.error('检查药品批次数据失败:', err.message);
    } else if (row.count === 0) {
      console.log('插入示例药品批次数据...');
      // 插入示例药品批次
      db.run(`
        INSERT INTO 药品批次 (药品编号, 批号, 生产日期, 有效期, 数量, 进价)
        VALUES
        (1, 'BN20230101', '2023-01-01', '2025-01-01', 120, 28.5),
        (2, 'BN20230102', '2023-01-02', '2025-01-02', 85, 12.8),
        (3, 'BN20230103', '2023-01-03', '2025-01-03', 64, 18.2),
        (4, 'BN20230104', '2023-01-04', '2025-01-04', 76, 24.5),
        (5, 'BN20230105', '2023-01-05', '2025-01-05', 180, 8.6)
      `);
    } else {
      console.log('药品批次数据已存在，跳过插入');
    }
  });
});

// 关闭数据库连接
db.close((err) => {
  if (err) {
    console.error('关闭数据库失败:', err.message);
    process.exit(1);
  }
  console.log('数据库初始化完成');
});
