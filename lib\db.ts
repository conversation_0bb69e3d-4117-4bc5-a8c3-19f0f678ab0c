import sqlite3 from 'sqlite3';
import { open, Database } from 'sqlite';

// 数据库连接单例
let db: Database | null = null;

/**
 * 获取数据库连接
 * @returns SQLite数据库连接
 */
export async function getDbConnection() {
  if (db) {
    return db;
  }

  try {
    // 确保在服务器端运行
    if (typeof window === 'undefined') {
      // 使用相对路径，避免使用Node.js特定的API
      const dbPath = './database.db';

      console.log(`连接数据库: ${dbPath}`);

      db = await open({
        filename: dbPath,
        driver: sqlite3.Database
      });

      return db;
    }

    throw new Error('数据库连接只能在服务器端使用');
  } catch (error) {
    console.error('数据库连接错误:', error);
    throw error;
  }
}

/**
 * 执行SQL查询
 * @param sql SQL语句
 * @param params 查询参数
 * @returns 查询结果
 */
export async function query(sql: string, params: any[] = []) {
  try {
    const connection = await getDbConnection();
    return await connection.all(sql, params);
  } catch (error) {
    console.error('数据库查询错误:', error);
    throw error;
  }
}

/**
 * 获取单条记录
 * @param sql SQL语句
 * @param params 查询参数
 * @returns 单条记录
 */
export async function get(sql: string, params: any[] = []) {
  const connection = await getDbConnection();
  return connection.get(sql, params);
}

/**
 * 执行SQL语句（插入、更新、删除等）
 * @param sql SQL语句
 * @param params 查询参数
 * @returns 执行结果
 */
export async function run(sql: string, params: any[] = []) {
  const connection = await getDbConnection();
  return connection.run(sql, params);
}