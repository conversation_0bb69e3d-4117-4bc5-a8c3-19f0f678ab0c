import { NextRequest, NextResponse } from 'next/server';
import { exec } from 'child_process';
import path from 'path';
import { promisify } from 'util';

const execPromise = promisify(exec);

export async function GET(request: NextRequest) {
  try {
    // 获取脚本路径
    const scriptPath = path.join(process.cwd(), 'scripts', 'fix-inventory-records-table.js');
    
    console.log(`执行数据库修复脚本: ${scriptPath}`);
    
    // 执行脚本
    const { stdout, stderr } = await execPromise(`node ${scriptPath}`);
    
    if (stderr) {
      console.error('脚本执行错误:', stderr);
      return NextResponse.json(
        { 
          success: false, 
          message: '数据库修复失败', 
          error: stderr 
        },
        { status: 500 }
      );
    }
    
    console.log('脚本执行输出:', stdout);
    
    return NextResponse.json({
      success: true,
      message: '数据库修复成功',
      details: stdout
    });
  } catch (error) {
    console.error('执行数据库修复脚本失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: '执行数据库修复脚本失败', 
        error: error instanceof Error ? error.message : '未知错误' 
      },
      { status: 500 }
    );
  }
}
