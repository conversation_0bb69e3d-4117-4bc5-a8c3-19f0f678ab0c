const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

// 数据库文件路径
const dbPath = path.resolve(process.cwd(), 'database.sqlite');

console.log(`修改products表结构: ${dbPath}`);

// 检查数据库文件是否存在
if (!fs.existsSync(dbPath)) {
  console.log('数据库文件不存在');
  process.exit(1);
}

// 创建数据库连接
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('连接数据库失败:', err.message);
    process.exit(1);
  }
  console.log('已连接到数据库');
});

// 禁用外键约束
db.run('PRAGMA foreign_keys = OFF', (err) => {
  if (err) {
    console.error('禁用外键约束失败:', err.message);
    db.close();
    process.exit(1);
  }
  
  console.log('已禁用外键约束');
  
  // 开始事务
  db.run('BEGIN TRANSACTION', (err) => {
    if (err) {
      console.error('开始事务失败:', err.message);
      db.close();
      process.exit(1);
    }
    
    console.log('开始事务');
    
    // 1. 创建临时表，不包含sku和unit字段
    db.run(`
      CREATE TABLE products_temp (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        generic_name TEXT,
        description TEXT,
        barcode TEXT,
        category_id INTEGER,
        manufacturer TEXT,
        approval_number TEXT,
        specification TEXT,
        dosage_form TEXT,
        price REAL NOT NULL,
        cost_price REAL,
        stock_quantity INTEGER DEFAULT 0,
        min_stock_level INTEGER DEFAULT 0,
        is_prescription BOOLEAN DEFAULT 0,
        is_medical_insurance BOOLEAN DEFAULT 0,
        storage_condition TEXT,
        status TEXT DEFAULT 'active',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES categories(id)
      )
    `, (err) => {
      if (err) {
        console.error('创建临时表失败:', err.message);
        db.run('ROLLBACK');
        db.close();
        process.exit(1);
      }
      
      console.log('临时表创建成功');
      
      // 2. 复制数据到临时表，不包含sku和unit字段
      db.run(`
        INSERT INTO products_temp (
          id, name, generic_name, description, barcode, category_id,
          manufacturer, approval_number, specification, dosage_form,
          price, cost_price, stock_quantity, min_stock_level,
          is_prescription, is_medical_insurance, storage_condition, status,
          created_at, updated_at
        )
        SELECT
          id, name, generic_name, description, barcode, category_id,
          manufacturer, approval_number, specification, dosage_form,
          price, cost_price, stock_quantity, min_stock_level,
          is_prescription, is_medical_insurance, storage_condition, status,
          created_at, updated_at
        FROM products
      `, (err) => {
        if (err) {
          console.error('复制数据失败:', err.message);
          db.run('ROLLBACK');
          db.close();
          process.exit(1);
        }
        
        console.log('数据复制成功');
        
        // 3. 删除原表
        db.run('DROP TABLE products', (err) => {
          if (err) {
            console.error('删除原表失败:', err.message);
            db.run('ROLLBACK');
            db.close();
            process.exit(1);
          }
          
          console.log('原表删除成功');
          
          // 4. 重命名临时表为原表名
          db.run('ALTER TABLE products_temp RENAME TO products', (err) => {
            if (err) {
              console.error('重命名临时表失败:', err.message);
              db.run('ROLLBACK');
              db.close();
              process.exit(1);
            }
            
            console.log('临时表重命名成功');
            
            // 5. 重新创建索引
            db.run('CREATE INDEX IF NOT EXISTS idx_products_name ON products(name)', (err) => {
              if (err) {
                console.error('创建索引失败:', err.message);
                db.run('ROLLBACK');
                db.close();
                process.exit(1);
              }
              
              console.log('索引创建成功');
              
              // 6. 提交事务
              db.run('COMMIT', (err) => {
                if (err) {
                  console.error('提交事务失败:', err.message);
                  db.run('ROLLBACK');
                  db.close();
                  process.exit(1);
                }
                
                console.log('事务提交成功');
                
                // 查询更新后的表结构
                db.all("PRAGMA table_info(products)", (err, columns) => {
                  if (err) {
                    console.error('查询表结构失败:', err.message);
                    db.close();
                    process.exit(1);
                  }
                  
                  console.log('更新后的products表结构:');
                  columns.forEach(column => {
                    console.log(`  - ${column.name} (${column.type})`);
                  });
                  
                  // 重新启用外键约束
                  db.run('PRAGMA foreign_keys = ON', (err) => {
                    if (err) {
                      console.error('启用外键约束失败:', err.message);
                    } else {
                      console.log('已重新启用外键约束');
                    }
                    
                    // 关闭数据库连接
                    db.close((err) => {
                      if (err) {
                        console.error('关闭数据库失败:', err.message);
                        process.exit(1);
                      }
                      console.log('数据库连接已关闭');
                    });
                  });
                });
              });
            });
          });
        });
      });
    });
  });
});
