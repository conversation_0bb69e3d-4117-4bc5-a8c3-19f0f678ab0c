const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

// 数据库文件路径
const dbPath = path.resolve(process.cwd(), 'database.sqlite');

console.log(`检查inventory_records表: ${dbPath}`);

// 检查数据库文件是否存在
if (!fs.existsSync(dbPath)) {
  console.log('数据库文件不存在');
  process.exit(1);
}

// 创建数据库连接
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('连接数据库失败:', err.message);
    process.exit(1);
  }
  console.log('已连接到数据库');
});

// 检查inventory_records表是否存在
db.get("SELECT name FROM sqlite_master WHERE type='table' AND name='inventory_records'", (err, row) => {
  if (err) {
    console.error('查询表失败:', err.message);
    db.close();
    process.exit(1);
  }
  
  if (row) {
    console.log('inventory_records表已存在');
  } else {
    console.log('创建inventory_records表...');
    
    // 创建inventory_records表
    db.run(`
      CREATE TABLE inventory_records (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        product_id INTEGER NOT NULL,
        batch_id INTEGER,
        quantity_change INTEGER NOT NULL,
        change_type TEXT NOT NULL,
        reference_id INTEGER,
        reference_type TEXT,
        note TEXT,
        operator_id INTEGER,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (product_id) REFERENCES products(id),
        FOREIGN KEY (batch_id) REFERENCES product_batches(id)
      )
    `, (err) => {
      if (err) {
        console.error('创建inventory_records表失败:', err.message);
      } else {
        console.log('inventory_records表创建成功');
      }
    });
  }
  
  // 关闭数据库连接
  setTimeout(() => {
    db.close((err) => {
      if (err) {
        console.error('关闭数据库失败:', err.message);
        process.exit(1);
      }
      console.log('数据库连接已关闭');
    });
  }, 1000); // 等待1秒，确保所有操作完成
});
