const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

// 数据库文件路径
const dbPath = path.resolve(process.cwd(), 'database.sqlite');

console.log(`创建缺失的表: ${dbPath}`);

// 检查数据库文件是否存在
if (!fs.existsSync(dbPath)) {
  console.log('数据库文件不存在');
  process.exit(1);
}

// 创建数据库连接
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('连接数据库失败:', err.message);
    process.exit(1);
  }
  console.log('已连接到数据库');
});

// 创建必要的表
db.serialize(() => {
  // 创建customers表
  db.run(`
    CREATE TABLE IF NOT EXISTS customers (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      phone TEXT,
      address TEXT,
      note TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
  `, (err) => {
    if (err) {
      console.error('创建customers表失败:', err.message);
    } else {
      console.log('customers表已创建或已存在');
    }
  });

  // 创建orders表
  db.run(`
    CREATE TABLE IF NOT EXISTS orders (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      order_number TEXT NOT NULL UNIQUE,
      customer_id INTEGER,
      operator_id INTEGER DEFAULT 1,
      total_amount REAL NOT NULL,
      payment_method TEXT DEFAULT 'cash',
      status TEXT DEFAULT 'completed',
      note TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (customer_id) REFERENCES customers(id)
    )
  `, (err) => {
    if (err) {
      console.error('创建orders表失败:', err.message);
    } else {
      console.log('orders表已创建或已存在');
    }
  });

  // 创建product_batches表
  db.run(`
    CREATE TABLE IF NOT EXISTS product_batches (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      product_id INTEGER NOT NULL,
      batch_number TEXT,
      production_date DATE,
      expiry_date DATE,
      quantity INTEGER DEFAULT 0,
      cost_price REAL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (product_id) REFERENCES products(id)
    )
  `, (err) => {
    if (err) {
      console.error('创建product_batches表失败:', err.message);
    } else {
      console.log('product_batches表已创建或已存在');
    }
  });

  // 创建order_details表
  db.run(`
    CREATE TABLE IF NOT EXISTS order_details (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      order_id INTEGER NOT NULL,
      product_id INTEGER NOT NULL,
      batch_id INTEGER,
      quantity INTEGER NOT NULL,
      unit_price REAL NOT NULL,
      subtotal REAL NOT NULL,
      FOREIGN KEY (order_id) REFERENCES orders(id),
      FOREIGN KEY (product_id) REFERENCES products(id),
      FOREIGN KEY (batch_id) REFERENCES product_batches(id)
    )
  `, (err) => {
    if (err) {
      console.error('创建order_details表失败:', err.message);
    } else {
      console.log('order_details表已创建或已存在');
    }
  });

  // 插入示例数据
  db.get("SELECT COUNT(*) as count FROM customers", (err, row) => {
    if (err) {
      console.error('检查customers数据失败:', err.message);
    } else if (row.count === 0) {
      console.log('插入示例客户数据...');
      db.run(`
        INSERT INTO customers (name, phone, address)
        VALUES 
        ('张三', '13800138001', '北京市海淀区'),
        ('李四', '13800138002', '上海市浦东新区'),
        ('王五', '13800138003', '广州市天河区')
      `);
    } else {
      console.log('customers表已有数据，跳过插入');
    }
  });

  // 检查products表是否有数据
  db.get("SELECT COUNT(*) as count FROM products", (err, row) => {
    if (err) {
      console.error('检查products数据失败:', err.message);
    } else if (row.count === 0) {
      console.log('插入示例产品数据...');
      db.run(`
        INSERT INTO products (name, generic_name, specification, manufacturer, category_id, stock_quantity, price, cost_price, sku)
        VALUES 
        ('阿莫西林胶囊', '阿莫西林胶囊', '0.25g*24粒/盒', '哈药集团制药总厂', 1, 120, 36.8, 28.5, 'P001'),
        ('布洛芬片', '布洛芬片', '0.1g*24片/盒', '上海信谊药厂有限公司', 1, 85, 18.5, 12.8, 'P002'),
        ('感冒灵颗粒', '感冒灵颗粒', '10g*9袋/盒', '哈药集团制药总厂', 1, 64, 25.6, 18.2, 'P003')
      `);
    } else {
      console.log('products表已有数据，跳过插入');
    }
  });

  // 检查product_batches表是否有数据
  db.get("SELECT COUNT(*) as count FROM product_batches", (err, row) => {
    if (err) {
      console.error('检查product_batches数据失败:', err.message);
    } else if (row.count === 0) {
      console.log('插入示例批次数据...');
      db.run(`
        INSERT INTO product_batches (product_id, batch_number, production_date, expiry_date, quantity, cost_price)
        VALUES 
        (1, 'BN20230101', '2023-01-01', '2025-01-01', 120, 28.5),
        (2, 'BN20230102', '2023-01-02', '2025-01-02', 85, 12.8),
        (3, 'BN20230103', '2023-01-03', '2025-01-03', 64, 18.2)
      `);
    } else {
      console.log('product_batches表已有数据，跳过插入');
    }
  });
});

// 关闭数据库连接
setTimeout(() => {
  db.close((err) => {
    if (err) {
      console.error('关闭数据库失败:', err.message);
      process.exit(1);
    }
    console.log('数据库连接已关闭');
  });
}, 1000); // 等待1秒，确保所有操作完成
