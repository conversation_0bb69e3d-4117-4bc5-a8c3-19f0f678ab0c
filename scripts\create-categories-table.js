const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

// 数据库文件路径
const dbPath = path.resolve(process.cwd(), 'database.sqlite');

console.log(`检查categories表: ${dbPath}`);

// 检查数据库文件是否存在
if (!fs.existsSync(dbPath)) {
  console.log('数据库文件不存在');
  process.exit(1);
}

// 创建数据库连接
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('连接数据库失败:', err.message);
    process.exit(1);
  }
  console.log('已连接到数据库');
});

// 检查categories表是否存在
db.get("SELECT name FROM sqlite_master WHERE type='table' AND name='categories'", (err, row) => {
  if (err) {
    console.error('查询表失败:', err.message);
    db.close();
    process.exit(1);
  }
  
  if (row) {
    console.log('categories表已存在');
  } else {
    console.log('创建categories表...');
    
    // 创建categories表
    db.run(`
      CREATE TABLE categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        description TEXT,
        parent_id INTEGER,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (parent_id) REFERENCES categories(id)
      )
    `, (err) => {
      if (err) {
        console.error('创建categories表失败:', err.message);
      } else {
        console.log('categories表创建成功');
        
        // 插入默认分类数据
        db.run(`
          INSERT INTO categories (name, description)
          VALUES 
          ('抗生素', '抗菌药物'),
          ('解热镇痛', '缓解疼痛和发热'),
          ('感冒用药', '治疗感冒症状'),
          ('清热解毒', '清热解毒类中药'),
          ('维生素类', '补充维生素')
        `, (err) => {
          if (err) {
            console.error('插入默认分类数据失败:', err.message);
          } else {
            console.log('默认分类数据插入成功');
          }
        });
      }
    });
  }
  
  // 关闭数据库连接
  setTimeout(() => {
    db.close((err) => {
      if (err) {
        console.error('关闭数据库失败:', err.message);
        process.exit(1);
      }
      console.log('数据库连接已关闭');
    });
  }, 1000); // 等待1秒，确保所有操作完成
});
