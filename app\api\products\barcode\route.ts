import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';

// 通过条形码或追溯码查询药品
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const code = searchParams.get('code');

    if (!code) {
      return NextResponse.json(
        { success: false, message: '条码不能为空' },
        { status: 400 }
      );
    }

    // 查询药品 - 同时匹配条形码和追溯码
    const products = await query(
      `SELECT
        p.id as id,
        p.name as name,
        p.generic_name as generic_name,
        p.description as description,
        p.barcode as barcode,
        p.trace_code as trace_code,
        p.category_id as category_id,
        c.name as category_name,
        p.manufacturer as manufacturer,
        p.approval_number as approval_number,
        p.specification as specification,
        p.dosage_form as dosage_form,
        p.price as price,
        p.cost_price as cost_price,
        p.stock_quantity as stock_quantity,
        p.min_stock_level as min_stock_level,
        p.is_prescription as is_prescription,
        p.is_medical_insurance as is_medical_insurance,
        p.storage_condition as storage_condition,
        p.status as status
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.barcode = ? OR p.trace_code = ?
      LIMIT 1`,
      [code, code]
    );

    if (!products || products.length === 0) {
      return NextResponse.json(
        { success: false, message: '未找到匹配的药品' },
        { status: 404 }
      );
    }

    // 处理布尔值转换
    const processedProduct = {
      ...products[0],
      is_prescription: products[0].is_prescription === 1,
      is_medical_insurance: products[0].is_medical_insurance === 1
    };

    return NextResponse.json({
      success: true,
      data: processedProduct
    });
  } catch (error) {
    console.error('通过条码查询药品失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '通过条码查询药品失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
