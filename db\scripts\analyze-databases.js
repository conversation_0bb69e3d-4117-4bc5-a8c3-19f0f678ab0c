const sqlite3 = require('sqlite3');
const { open } = require('sqlite');
const fs = require('fs');
const path = require('path');

async function analyzeDatabase() {
  console.log('=== 数据库文件分析 ===\n');
  
  // 检查所有数据库文件
  const dbFiles = [
    './database.db',
    './database.sqlite', 
    './db/retail.db'
  ];
  
  const analysis = {
    files: [],
    activeDatabase: null,
    recommendations: []
  };
  
  for (const dbFile of dbFiles) {
    console.log(`检查文件: ${dbFile}`);
    const fileInfo = {
      path: dbFile,
      exists: false,
      size: 0,
      tables: [],
      totalRecords: 0,
      lastModified: null
    };
    
    if (fs.existsSync(dbFile)) {
      const stats = fs.statSync(dbFile);
      fileInfo.exists = true;
      fileInfo.size = stats.size;
      fileInfo.lastModified = stats.mtime;
      
      console.log(`  文件大小: ${stats.size} bytes`);
      console.log(`  修改时间: ${stats.mtime}`);
      
      try {
        const db = await open({
          filename: dbFile,
          driver: sqlite3.Database
        });
        
        // 获取表列表
        const tables = await db.all('SELECT name FROM sqlite_master WHERE type="table"');
        console.log(`  表数量: ${tables.length}`);
        
        if (tables.length > 0) {
          console.log('  表列表:');
          for (const table of tables) {
            try {
              const count = await db.get(`SELECT COUNT(*) as count FROM ${table.name}`);
              const tableInfo = {
                name: table.name,
                records: count.count
              };
              fileInfo.tables.push(tableInfo);
              fileInfo.totalRecords += count.count;
              console.log(`    - ${table.name} (${count.count} 条记录)`);
            } catch (tableError) {
              console.log(`    - ${table.name} (查询失败: ${tableError.message})`);
            }
          }
        }
        
        await db.close();
      } catch (error) {
        console.log(`  错误: ${error.message}`);
        fileInfo.error = error.message;
      }
    } else {
      console.log('  文件不存在');
    }
    
    analysis.files.push(fileInfo);
    console.log('');
  }
  
  // 确定活跃数据库
  const existingFiles = analysis.files.filter(f => f.exists && f.size > 0);
  if (existingFiles.length > 0) {
    // 选择记录最多的数据库作为活跃数据库
    analysis.activeDatabase = existingFiles.reduce((prev, current) => 
      (prev.totalRecords > current.totalRecords) ? prev : current
    );
  }
  
  // 生成建议
  if (analysis.files.length > 1) {
    analysis.recommendations.push('发现多个数据库文件，建议统一到db目录');
  }
  
  if (analysis.activeDatabase) {
    analysis.recommendations.push(`当前活跃数据库: ${analysis.activeDatabase.path}`);
  }
  
  return analysis;
}

// 检查脚本文件
function analyzeScripts() {
  console.log('=== 数据库脚本文件分析 ===\n');
  
  const scriptDirs = ['./scripts', './app/db', './db'];
  const scripts = [];
  
  for (const dir of scriptDirs) {
    if (fs.existsSync(dir)) {
      const files = fs.readdirSync(dir);
      for (const file of files) {
        const filePath = path.join(dir, file);
        const stats = fs.statSync(filePath);
        
        if (stats.isFile() && (file.endsWith('.js') || file.endsWith('.ts') || file.endsWith('.sql'))) {
          scripts.push({
            path: filePath,
            name: file,
            size: stats.size,
            type: path.extname(file)
          });
        }
      }
    }
  }
  
  console.log('数据库相关脚本:');
  scripts.forEach(script => {
    console.log(`  - ${script.path} (${script.size} bytes)`);
  });
  
  return scripts;
}

async function main() {
  try {
    const dbAnalysis = await analyzeDatabase();
    const scriptAnalysis = analyzeScripts();
    
    console.log('\n=== 分析总结 ===');
    console.log(`发现 ${dbAnalysis.files.filter(f => f.exists).length} 个数据库文件`);
    console.log(`发现 ${scriptAnalysis.length} 个相关脚本文件`);
    
    if (dbAnalysis.activeDatabase) {
      console.log(`活跃数据库: ${dbAnalysis.activeDatabase.path}`);
      console.log(`总记录数: ${dbAnalysis.activeDatabase.totalRecords}`);
    }
    
    console.log('\n建议:');
    dbAnalysis.recommendations.forEach(rec => {
      console.log(`  - ${rec}`);
    });
    
    // 保存分析结果到JSON文件
    const result = {
      timestamp: new Date().toISOString(),
      databases: dbAnalysis,
      scripts: scriptAnalysis
    };
    
    fs.writeFileSync('database-analysis.json', JSON.stringify(result, null, 2));
    console.log('\n分析结果已保存到 database-analysis.json');
    
  } catch (error) {
    console.error('分析失败:', error);
  }
}

main();
