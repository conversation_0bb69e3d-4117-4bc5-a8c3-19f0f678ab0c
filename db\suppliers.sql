-- 创建供应商表
CREATE TABLE IF NOT EXISTS suppliers (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,                    -- 供应商名称
  contact_person TEXT,                   -- 联系人
  phone TEXT,                           -- 联系电话
  email TEXT,                           -- 电子邮箱
  address TEXT,                         -- 地址
  tax_number TEXT,                      -- 税号
  bank_name TEXT,                       -- 开户银行
  bank_account TEXT,                    -- 银行账号
  license_number TEXT NOT NULL,         -- 药品经营许可证号
  license_expiry_date TEXT NOT NULL,    -- 许可证有效期
  gsp_certificate TEXT,                 -- GSP认证证书号
  gsp_expiry_date TEXT,                 -- GSP认证有效期
  business_scope TEXT,                  -- 经营范围（如：中药材、中成药、化学药制剂等）
  quality_officer TEXT,                  -- 质量负责人
  status TEXT DEFAULT 'active',         -- 状态：active-活跃，inactive-停用
  notes TEXT,                           -- 备注
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建供应商状态索引
CREATE INDEX IF NOT EXISTS idx_suppliers_status ON suppliers(status);

-- 添加示例数据
INSERT INTO suppliers (name, contact_person, phone, email, address, tax_number, bank_name, bank_account, license_number, license_expiry_date, gsp_certificate, gsp_expiry_date, business_scope, quality_officer, status, notes)
VALUES 
('广州医药有限公司', '张三', '***********', '<EMAIL>', '广州市天河区医药园区A栋101', '91440101XXXXXXXX', '中国工商银行', '6222021234567890123', 'C-GD12345', '2025-12-31', 'GSP-GD67890', '2025-12-31', '中成药、化学药制剂、抗生素', '李明', 'active', '主要药品供应商'),
('深圳医药批发商', '李四', '***********', '<EMAIL>', '深圳市福田区医药物流园123号', '91440301XXXXXXXX', '中国建设银行', '6227002234567890123', 'C-GD23456', '2025-12-31', 'GSP-GD78901', '2025-12-31', '中药材、中药饮片', '王华', 'active', '中药材供应商'),
('东莞医药连锁配送中心', '王五', '***********', '<EMAIL>', '东莞市长安镇医药工业园B区', '91441900XXXXXXXX', '中国农业银行', '6228481234567890123', 'C-GD34567', '2025-12-31', 'GSP-GD89012', '2025-12-31', '化学药制剂、生物制品', '赵强', 'active', '主要药品供应商');