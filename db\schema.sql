-- 药店零售管理系统数据库架构

-- 角色表 - 存储系统角色信息
DROP TABLE IF EXISTS 角色;
CREATE TABLE 角色 (
    编号 INTEGER PRIMARY KEY AUTOINCREMENT,
    名称 TEXT NOT NULL UNIQUE,
    描述 TEXT,
    权限列表 TEXT NOT NULL, -- JSON格式存储权限
    创建时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户角色映射表
DROP TABLE IF EXISTS 用户角色映射;
CREATE TABLE 用户角色映射 (
    用户编号 INTEGER NOT NULL,
    角色编号 INTEGER NOT NULL,
    创建时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (用户编号, 角色编号),
    FOREIGN KEY (用户编号) REFERENCES 用户(编号) ON DELETE CASCADE,
    FOREIGN KEY (角色编号) REFERENCES 角色(编号) ON DELETE CASCADE
);

-- 用户表 - 存储系统用户信息
DROP TABLE IF EXISTS 用户;
CREATE TABLE 用户 (
    编号 INTEGER PRIMARY KEY AUTOINCREMENT,
    登录名 TEXT NOT NULL UNIQUE,
    密码 TEXT NOT NULL,
    姓名 TEXT NOT NULL,
    执业证号 TEXT,
    证件有效期 TEXT CHECK (证件有效期 IS NULL OR 证件有效期 GLOB '[0-9][0-9][0-9][0-9]-[0-9][0-9]-[0-9][0-9]'),
    创建时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 药品附加信息表
DROP TABLE IF EXISTS 药品附加信息;
CREATE TABLE 药品附加信息 (
    药品编号 INTEGER PRIMARY KEY,
    说明书 TEXT,
    图片URL TEXT,
    视频URL TEXT,
    注意事项 TEXT,
    不良反应 TEXT,
    禁忌 TEXT,
    药物相互作用 TEXT,
    创建时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (药品编号) REFERENCES 药品(编号) ON DELETE CASCADE
);

-- 操作日志表
DROP TABLE IF EXISTS 操作日志;
CREATE TABLE 操作日志 (
    编号 INTEGER PRIMARY KEY AUTOINCREMENT,
    用户编号 INTEGER NOT NULL,
    操作类型 TEXT NOT NULL,
    操作对象 TEXT NOT NULL,
    对象编号 INTEGER,
    操作详情 TEXT,
    操作时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    IP地址 TEXT,
    终端设备 TEXT,
    FOREIGN KEY (用户编号) REFERENCES 用户(编号) ON DELETE SET NULL
);

-- 药品分类表 - 存储药品分类信息
DROP TABLE IF EXISTS 药品分类;
CREATE TABLE 药品分类 (
    编号 INTEGER PRIMARY KEY AUTOINCREMENT, -- 分类ID，自增主键
    名称 TEXT NOT NULL,                   -- 分类名称（如：中成药、西药、保健品等）
    描述 TEXT,                     -- 分类描述
    父分类编号 INTEGER,                    -- 父分类ID，用于构建分类层级
    创建时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 更新时间
    FOREIGN KEY (父分类编号) REFERENCES 药品分类(编号) -- 关联到父分类
);

-- 药品表 - 存储药品基本信息
DROP TABLE IF EXISTS 药品;
CREATE TABLE 药品 (
    编号 INTEGER PRIMARY KEY AUTOINCREMENT,
    名称 TEXT NOT NULL,
    通用名 TEXT,
    描述 TEXT,
    库存编码 TEXT UNIQUE,
    条形码 TEXT,
    分类编号 INTEGER,
    生产厂家 TEXT,
    批准文号 TEXT,
    规格 TEXT,
    剂型 TEXT,
    单位 TEXT,
    售价 REAL NOT NULL CHECK (售价 >= 0),
    成本价 REAL CHECK (成本价 >= 0),
    库存数量 INTEGER DEFAULT 0 CHECK (库存数量 >= 0),
    最低库存 INTEGER DEFAULT 5 CHECK (最低库存 >= 0),
    是否处方药 INTEGER DEFAULT 0 CHECK (是否处方药 IN (0, 1)),
    是否医保 INTEGER DEFAULT 0 CHECK (是否医保 IN (0, 1)),
    储存条件 TEXT,
    状态 TEXT DEFAULT 'active' CHECK (状态 IN ('active', 'inactive')),
    创建时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (分类编号) REFERENCES 药品分类(编号) ON DELETE SET NULL
);

-- 药品批次表 - 存储药品批次信息
DROP TABLE IF EXISTS 药品批次;
CREATE TABLE 药品批次 (
    编号 INTEGER PRIMARY KEY AUTOINCREMENT,
    药品编号 INTEGER NOT NULL,
    批号 TEXT NOT NULL,
    生产日期 TEXT CHECK (生产日期 GLOB '[0-9][0-9][0-9][0-9]-[0-9][0-9]-[0-9][0-9]'),
    有效期 TEXT NOT NULL CHECK (有效期 GLOB '[0-9][0-9][0-9][0-9]-[0-9][0-9]-[0-9][0-9]'),
    数量 INTEGER NOT NULL CHECK (数量 > 0),
    供应商编号 INTEGER NOT NULL,
    进货价 REAL NOT NULL CHECK (进货价 >= 0),
    创建时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (药品编号) REFERENCES 药品(编号) ON DELETE CASCADE,
    FOREIGN KEY (供应商编号) REFERENCES 供应商(编号) ON DELETE CASCADE
);

-- 客户表 - 存储客户信息
DROP TABLE IF EXISTS 客户;
CREATE TABLE 客户 (
    编号 INTEGER PRIMARY KEY AUTOINCREMENT, -- 客户ID，自增主键
    姓名 TEXT NOT NULL,                   -- 客户姓名
    电话 TEXT,                           -- 联系电话
    邮箱 TEXT,                           -- 电子邮箱
    地址 TEXT,                         -- 客户地址
    身份证号 TEXT,                       -- 身份证号
    医保卡号 TEXT,        -- 医保卡号
    会员等级 TEXT DEFAULT 'regular', -- 会员等级（regular-普通，vip-贵宾等）
    积分 INTEGER DEFAULT 0,              -- 会员积分
    创建时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP  -- 更新时间
);

-- 处方表 - 存储处方信息
DROP TABLE IF EXISTS 处方;
CREATE TABLE 处方 (
    编号 INTEGER PRIMARY KEY AUTOINCREMENT, -- 处方ID，自增主键
    处方编号 TEXT NOT NULL UNIQUE, -- 处方编号
    客户编号 INTEGER NOT NULL,         -- 患者ID
    医生姓名 TEXT NOT NULL,            -- 开方医生
    医院 TEXT,                        -- 开方医院
    诊断信息 TEXT,                       -- 诊断信息
    开方日期 TEXT NOT NULL,      -- 开方日期
    药师编号 INTEGER NOT NULL,       -- 审方药师ID
    状态 TEXT DEFAULT 'pending',        -- 处方状态（pending-待审核，approved-已审核，rejected-已驳回）
    备注 TEXT,                           -- 备注
    创建时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 更新时间
    FOREIGN KEY (客户编号) REFERENCES 客户(编号), -- 关联到患者
    FOREIGN KEY (药师编号) REFERENCES 用户(编号)   -- 关联到药师
);

-- 处方明细表 - 存储处方药品明细
DROP TABLE IF EXISTS 处方明细;
CREATE TABLE 处方明细 (
    编号 INTEGER PRIMARY KEY AUTOINCREMENT, -- 明细ID，自增主键
    处方编号 INTEGER NOT NULL,     -- 处方ID
    药品编号 INTEGER NOT NULL,          -- 药品ID
    数量 INTEGER NOT NULL,            -- 数量
    用药剂量 TEXT,                          -- 用药剂量
    用药频次 TEXT,                       -- 用药频次
    用药方法 TEXT,                    -- 用药方法
    用药天数 INTEGER,                         -- 用药天数
    创建时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 更新时间
    FOREIGN KEY (处方编号) REFERENCES 处方(编号), -- 关联到处方
    FOREIGN KEY (药品编号) REFERENCES 药品(编号)          -- 关联到药品
);

-- 订单表 - 存储销售订单信息
DROP TABLE IF EXISTS 订单;
CREATE TABLE 订单 (
    编号 INTEGER PRIMARY KEY AUTOINCREMENT, -- 订单ID，自增主键
    订单编号 TEXT NOT NULL UNIQUE,    -- 订单编号，唯一
    客户编号 INTEGER,                  -- 客户ID
    处方编号 INTEGER,              -- 处方ID（如果是处方药销售）
    操作员编号 INTEGER NOT NULL,             -- 操作员ID
    总金额 REAL NOT NULL,           -- 订单总金额
    折扣金额 REAL DEFAULT 0,       -- 折扣金额
    医保金额 REAL DEFAULT 0, -- 医保支付金额
    自费金额 REAL DEFAULT 0,       -- 自费金额
    支付方式 TEXT,                  -- 支付方式（现金、刷卡、支付宝等）
    支付状态 TEXT DEFAULT 'pending', -- 支付状态（pending-待支付，paid-已支付）
    订单状态 TEXT DEFAULT 'pending',   -- 订单状态（pending-待处理，completed-已完成等）
    下单时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 下单时间
    创建时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 更新时间
    FOREIGN KEY (客户编号) REFERENCES 客户(编号), -- 关联到客户
    FOREIGN KEY (处方编号) REFERENCES 处方(编号), -- 关联到处方
    FOREIGN KEY (操作员编号) REFERENCES 用户(编号)         -- 关联到操作员
);

-- 订单明细表 - 存储订单药品明细
DROP TABLE IF EXISTS 订单明细;
CREATE TABLE 订单明细 (
    编号 INTEGER PRIMARY KEY AUTOINCREMENT, -- 明细ID，自增主键
    订单编号 INTEGER NOT NULL,            -- 订单ID
    药品编号 INTEGER NOT NULL,          -- 药品ID
    批次编号 INTEGER NOT NULL,            -- 药品批次ID
    数量 INTEGER NOT NULL,            -- 购买数量
    单价 REAL NOT NULL,            -- 单价
    折扣 REAL DEFAULT 0,              -- 药品折扣
    小计 REAL NOT NULL,              -- 小计金额
    创建时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 更新时间
    FOREIGN KEY (订单编号) REFERENCES 订单(编号),    -- 关联到订单
    FOREIGN KEY (药品编号) REFERENCES 药品(编号), -- 关联到药品
    FOREIGN KEY (批次编号) REFERENCES 药品批次(编号) -- 关联到药品批次
);

-- 库存变动记录表 - 记录药品库存变动
DROP TABLE IF EXISTS 库存变动记录;
CREATE TABLE 库存变动记录 (
    编号 INTEGER PRIMARY KEY AUTOINCREMENT, -- 交易ID，自增主键
    药品编号 INTEGER NOT NULL,          -- 药品ID
    批次编号 INTEGER NOT NULL,            -- 药品批次ID
    变动数量 INTEGER NOT NULL,            -- 变动数量（正数增加，负数减少）
    变动类型 TEXT NOT NULL,       -- 交易类型（purchase-采购，sale-销售，adjustment-调整，expired-过期）
    关联单据编号 INTEGER,                 -- 关联单据ID（订单ID或采购单ID）
    关联单据类型 TEXT,                  -- 关联单据类型（order-订单，purchase-采购单）
    备注 TEXT,                           -- 备注说明
    操作员编号 INTEGER NOT NULL,             -- 操作员ID
    变动时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 交易时间
    创建时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,      -- 创建时间
    更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,      -- 更新时间
    FOREIGN KEY (药品编号) REFERENCES 药品(编号), -- 关联到药品
    FOREIGN KEY (批次编号) REFERENCES 药品批次(编号), -- 关联到药品批次
    FOREIGN KEY (操作员编号) REFERENCES 用户(编号)       -- 关联到操作员
);

-- 供应商表 - 存储供应商信息
DROP TABLE IF EXISTS 供应商;
CREATE TABLE 供应商 (
    编号 INTEGER PRIMARY KEY AUTOINCREMENT, -- 供应商ID，自增主键
    名称 TEXT NOT NULL,                   -- 供应商名称
    联系人 TEXT,                  -- 联系人
    电话 TEXT,                           -- 联系电话
    邮箱 TEXT,                           -- 电子邮箱
    地址 TEXT,                         -- 供应商地址
    税号 TEXT,                      -- 税号
    开户银行 TEXT,                       -- 开户银行
    银行账号 TEXT,                    -- 银行账号
    许可证号 TEXT NOT NULL,         -- 药品经营许可证号
    许可证有效期 TEXT NOT NULL,    -- 许可证有效期
    GSP证书号 TEXT,                 -- GSP认证证书号
    GSP有效期 TEXT,                 -- GSP认证有效期
    经营范围 TEXT,                  -- 经营范围（如：中药材、中成药、化学药制剂等）
    质量负责人 TEXT,                  -- 质量负责人
    状态 TEXT DEFAULT 'active',         -- 状态：active-活跃，inactive-停用
    备注 TEXT,                           -- 备注
    创建时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP  -- 更新时间
);

-- 采购订单表 - 存储采购订单信息
DROP TABLE IF EXISTS 采购订单;
CREATE TABLE 采购订单 (
    编号 INTEGER PRIMARY KEY AUTOINCREMENT, -- 采购单ID，自增主键
    订单编号 TEXT NOT NULL UNIQUE,    -- 采购单编号，唯一
    供应商编号 INTEGER NOT NULL,         -- 供应商ID
    操作员编号 INTEGER NOT NULL,             -- 采购员ID
    总金额 REAL NOT NULL,           -- 采购总金额
    订单状态 TEXT DEFAULT 'pending',   -- 订单状态（pending-待处理，approved-已审核，received-已入库，rejected-已驳回）
    审核人编号 INTEGER,                -- 审核人ID
    审核时间 TIMESTAMP,               -- 审核时间
    审核备注 TEXT,                    -- 审核备注
    预计到货日期 TEXT,              -- 预计到货日期
    实际到货日期 TEXT,              -- 实际到货日期
    创建时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 更新时间
    FOREIGN KEY (供应商编号) REFERENCES 供应商(编号), -- 关联到供应商
    FOREIGN KEY (操作员编号) REFERENCES 用户(编号),    -- 关联到采购员
    FOREIGN KEY (审核人编号) REFERENCES 用户(编号)     -- 关联到审核人
);

-- 采购订单明细表 - 存储采购订单药品明细
DROP TABLE IF EXISTS 采购订单明细;
CREATE TABLE 采购订单明细 (
    编号 INTEGER PRIMARY KEY AUTOINCREMENT, -- 明细ID，自增主键
    采购单编号 INTEGER NOT NULL,         -- 采购单ID
    药品编号 INTEGER NOT NULL,          -- 药品ID
    采购数量 INTEGER NOT NULL,            -- 采购数量
    单价 REAL NOT NULL,            -- 采购单价
    小计 REAL NOT NULL,              -- 小计金额
    生产批号 TEXT,                    -- 生产批号
    生产日期 TEXT,                    -- 生产日期
    有效期 TEXT,                     -- 有效期
    创建时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 更新时间
    FOREIGN KEY (采购单编号) REFERENCES 采购订单(编号), -- 关联到采购单
    FOREIGN KEY (药品编号) REFERENCES 药品(编号)      -- 关联到药品
);

-- 创建供应商状态索引
CREATE INDEX IF NOT EXISTS idx_gongyingshang_status ON 供应商(状态);

-- 创建药品状态索引
CREATE INDEX IF NOT EXISTS idx_yaopin_status ON 药品(状态);

-- 创建处方状态索引
CREATE INDEX IF NOT EXISTS idx_chufang_status ON 处方(状态);

-- 创建订单状态索引
CREATE INDEX IF NOT EXISTS idx_dingdan_status ON 订单(订单状态);

-- 创建采购单状态索引
CREATE INDEX IF NOT EXISTS idx_caigoudingdan_status ON 采购订单(订单状态);

-- 创建触发器
-- 药品表更新时间触发器
CREATE TRIGGER update_药品更新时间
AFTER UPDATE ON 药品
FOR EACH ROW
BEGIN
    UPDATE 药品 SET 更新时间 = CURRENT_TIMESTAMP WHERE 编号 = OLD.编号;
END;

-- 药品批次表更新时间触发器
CREATE TRIGGER update_药品批次更新时间
AFTER UPDATE ON 药品批次
FOR EACH ROW
BEGIN
    UPDATE 药品批次 SET 更新时间 = CURRENT_TIMESTAMP WHERE 编号 = OLD.编号;
END;

-- 创建更多索引
CREATE INDEX IF NOT EXISTS idx_药品_库存编码 ON 药品(库存编码);
CREATE INDEX IF NOT EXISTS idx_药品_名称 ON 药品(名称);
CREATE INDEX IF NOT EXISTS idx_客户_电话 ON 客户(电话);
CREATE INDEX IF NOT EXISTS idx_供应商_名称 ON 供应商(名称);
CREATE INDEX IF NOT EXISTS idx_订单_客户时间 ON 订单(客户编号, 下单时间);
CREATE INDEX IF NOT EXISTS idx_处方_客户时间 ON 处方(客户编号, 开方日期);
CREATE INDEX IF NOT EXISTS idx_药品批次_有效期 ON 药品批次(有效期);

-- 初始化角色数据
INSERT INTO 角色 (名称, 描述, 权限列表) VALUES 
('admin', '系统管理员', '{"all": true}'),
('pharmacist', '执业药师', '{"prescription": true, "drug": true, "inventory": true}'),
('staff', '销售员', '{"sale": true, "customer": true}');

-- 初始化管理员用户
INSERT INTO 用户 (登录名, 密码, 姓名) VALUES ('admin', 'admin123', '系统管理员');
INSERT INTO 用户角色映射 (用户编号, 角色编号) 
VALUES ((SELECT 编号 FROM 用户 WHERE 登录名 = 'admin'), (SELECT 编号 FROM 角色 WHERE 名称 = 'admin'));

-- 初始化药品分类
INSERT INTO 药品分类 (名称, 描述) VALUES ('中成药', '传统中成药制剂');
INSERT INTO 药品分类 (名称, 描述) VALUES ('化学药品', '西药、化学药品');
INSERT INTO 药品分类 (名称, 描述) VALUES ('中药材', '中药材、饮片');
INSERT INTO 药品分类 (名称, 描述) VALUES ('生物制品', '疫苗、血液制品等');
INSERT INTO 药品分类 (名称, 描述) VALUES ('医疗器械', '医疗器械、耗材');
INSERT INTO 药品分类 (名称, 描述) VALUES ('保健品', '保健食品、功能性食品'); -- 添加保健品分类

-- 初始化更多药品分类 (示例)
INSERT INTO 药品分类 (名称, 描述, 父分类编号) VALUES ('感冒用药', '治疗感冒相关症状', (SELECT 编号 FROM 药品分类 WHERE 名称 = '化学药品'));
INSERT INTO 药品分类 (名称, 描述, 父分类编号) VALUES ('维生素矿物质', '补充维生素和矿物质', (SELECT 编号 FROM 药品分类 WHERE 名称 = '保健品'));

-- 初始化供应商 (示例)
INSERT INTO 供应商 (名称, 联系人, 电话, 邮箱, 地址, 税号, 开户银行, 银行账号, 许可证号, 许可证有效期, GSP证书号, GSP有效期, 经营范围, 质量负责人, 状态)
VALUES 
('国药控股', '张三', '13800138000', '<EMAIL>', '北京市朝阳区', '91110105MA01DC8KXB', '中国工商银行', '6222020100123456789', '京YJY20230001', '2028-12-31', 'BJGSP20230001', '2028-12-31', '中成药、化学药制剂、生物制品', '李四', 'active'),
('华润医药', '王五', '13900139000', '<EMAIL>', '上海市浦东新区', '91310115MA1K3J7LXB', '中国建设银行', '6217001210098765432', '沪YJY20230002', '2028-11-30', 'SHGSP20230002', '2028-11-30', '化学药制剂、医疗器械', '赵六', 'active');

-- 初始化药品 (示例)
INSERT INTO 药品 (名称, 通用名, 描述, 库存编码, 条形码, 分类编号, 生产厂家, 批准文号, 规格, 剂型, 单位, 售价, 成本价, 库存数量, 最低库存, 是否处方药, 是否医保, 储存条件, 状态)
VALUES 
('999感冒灵颗粒', '复方感冒灵颗粒', '解热镇痛。用于感冒引起的头痛，发热，鼻塞，流涕，咽痛等。', 'GM001', '6921879700015', (SELECT 编号 FROM 药品分类 WHERE 名称 = '感冒用药'), '华润三九医药股份有限公司', '国药准字Z44021940', '10g*9袋', '颗粒剂', '盒', 15.5, 8.0, 100, 10, 0, 1, '密封，置阴凉干燥处。', 'active'),
('阿莫西林胶囊', '阿莫西林', '适用于敏感菌所致的感染。', 'AMXL001', '6938184800116', (SELECT 编号 FROM 药品分类 WHERE 名称 = '化学药品'), '珠海联邦制药股份有限公司中山分公司', '国药准字H44020970', '0.25g*24粒', '胶囊剂', '盒', 12.0, 5.5, 200, 20, 1, 1, '遮光，密封保存。', 'active'),
('维生素C片', '维生素C', '用于预防坏血病，也可用于各种急慢性传染疾病及紫癜等的辅助治疗。', 'VC001', '6920170800118', (SELECT 编号 FROM 药品分类 WHERE 名称 = '维生素矿物质'), '石药集团欧意药业有限公司', '国药准字H13022001', '0.1g*100片', '片剂', '瓶', 9.9, 3.0, 300, 30, 0, 0, '遮光，密封保存。', 'active');

-- 初始化药品批次 (示例) - 确保药品编号和供应商编号存在
INSERT INTO 药品批次 (药品编号, 批号, 生产日期, 有效期, 数量, 供应商编号, 进货价)
VALUES
((SELECT 编号 FROM 药品 WHERE 库存编码 = 'GM001'), '20230101', '2023-01-01', '2025-12-31', 50, (SELECT 编号 FROM 供应商 WHERE 名称 = '华润医药'), 7.8),
((SELECT 编号 FROM 药品 WHERE 库存编码 = 'AMXL001'), 'A20230201', '2023-02-01', '2026-01-31', 100, (SELECT 编号 FROM 供应商 WHERE 名称 = '国药控股'), 5.2),
((SELECT 编号 FROM 药品 WHERE 库存编码 = 'VC001'), 'VC230301', '2023-03-01', '2025-02-28', 150, (SELECT 编号 FROM 供应商 WHERE 名称 = '国药控股'), 2.8);

-- 更新药品初始库存 (基于批次)
UPDATE 药品 SET 库存数量 = (SELECT IFNULL(SUM(数量), 0) FROM 药品批次 WHERE 药品批次.药品编号 = 药品.编号) WHERE 编号 IN (SELECT DISTINCT 药品编号 FROM 药品批次);