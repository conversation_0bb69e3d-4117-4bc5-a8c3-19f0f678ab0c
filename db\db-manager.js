#!/usr/bin/env node

/**
 * 数据库管理工具
 * 提供数据库的创建、备份、恢复、分析等功能
 */

const sqlite3 = require('sqlite3');
const { open } = require('sqlite');
const fs = require('fs');
const path = require('path');

class DatabaseManager {
  constructor() {
    this.dbPath = './db/main.db';
    this.backupDir = './db/backups';
    this.schemaPath = './db/schema.sql';
  }

  async init() {
    // 确保目录存在
    if (!fs.existsSync('./db')) {
      fs.mkdirSync('./db', { recursive: true });
    }
    if (!fs.existsSync(this.backupDir)) {
      fs.mkdirSync(this.backupDir, { recursive: true });
    }
  }

  async createDatabase() {
    console.log('创建新数据库...');
    
    if (fs.existsSync(this.dbPath)) {
      const backup = await this.backup();
      console.log(`已备份现有数据库: ${backup}`);
    }

    const db = await open({
      filename: this.dbPath,
      driver: sqlite3.Database
    });

    // 执行schema文件
    if (fs.existsSync(this.schemaPath)) {
      const schema = fs.readFileSync(this.schemaPath, 'utf8');
      await db.exec(schema);
      console.log('✅ 数据库架构创建完成');
    } else {
      console.log('⚠️ 未找到schema文件，创建基本表结构');
      await this.createBasicTables(db);
    }

    await db.close();
    console.log(`✅ 数据库创建完成: ${this.dbPath}`);
  }

  async createBasicTables(db) {
    // 创建基本表结构
    const tables = [
      `CREATE TABLE IF NOT EXISTS settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        setting_name TEXT NOT NULL UNIQUE,
        setting_value TEXT,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`,
      
      `CREATE TABLE IF NOT EXISTS categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      `CREATE TABLE IF NOT EXISTS suppliers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        contact_person TEXT,
        phone TEXT,
        email TEXT,
        address TEXT,
        tax_number TEXT,
        bank_account TEXT,
        bank_name TEXT,
        status TEXT DEFAULT 'active',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      `CREATE TABLE IF NOT EXISTS products (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        generic_name TEXT,
        description TEXT,
        barcode TEXT UNIQUE,
        trace_code TEXT,
        category_id INTEGER,
        manufacturer TEXT,
        approval_number TEXT,
        specification TEXT,
        dosage_form TEXT,
        price REAL NOT NULL,
        cost_price REAL,
        stock_quantity INTEGER DEFAULT 0,
        min_stock_level INTEGER DEFAULT 0,
        is_prescription BOOLEAN DEFAULT 0,
        is_medical_insurance BOOLEAN DEFAULT 0,
        storage_condition TEXT,
        status TEXT DEFAULT 'active',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES categories(id)
      )`
    ];

    for (const sql of tables) {
      await db.exec(sql);
    }
  }

  async backup(customName = null) {
    if (!fs.existsSync(this.dbPath)) {
      throw new Error('数据库文件不存在');
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupName = customName || `backup-${timestamp}.db`;
    const backupPath = path.join(this.backupDir, backupName);

    fs.copyFileSync(this.dbPath, backupPath);
    console.log(`✅ 数据库备份完成: ${backupPath}`);
    return backupPath;
  }

  async restore(backupPath) {
    if (!fs.existsSync(backupPath)) {
      throw new Error(`备份文件不存在: ${backupPath}`);
    }

    // 备份当前数据库
    if (fs.existsSync(this.dbPath)) {
      await this.backup('before-restore');
    }

    fs.copyFileSync(backupPath, this.dbPath);
    console.log(`✅ 数据库恢复完成: ${backupPath} → ${this.dbPath}`);
  }

  async analyze() {
    if (!fs.existsSync(this.dbPath)) {
      console.log('❌ 数据库文件不存在');
      return;
    }

    const db = await open({
      filename: this.dbPath,
      driver: sqlite3.Database
    });

    console.log('=== 数据库分析报告 ===\n');

    // 基本信息
    const stats = fs.statSync(this.dbPath);
    console.log(`数据库文件: ${this.dbPath}`);
    console.log(`文件大小: ${stats.size} bytes`);
    console.log(`最后修改: ${stats.mtime}\n`);

    // 表信息
    const tables = await db.all("SELECT name FROM sqlite_master WHERE type='table'");
    console.log(`表数量: ${tables.length}`);
    console.log('表详情:');

    let totalRecords = 0;
    for (const table of tables) {
      try {
        const count = await db.get(`SELECT COUNT(*) as count FROM ${table.name}`);
        totalRecords += count.count;
        console.log(`  - ${table.name}: ${count.count} 条记录`);
      } catch (error) {
        console.log(`  - ${table.name}: 查询失败 (${error.message})`);
      }
    }

    console.log(`\n总记录数: ${totalRecords}`);

    await db.close();
  }

  async listBackups() {
    if (!fs.existsSync(this.backupDir)) {
      console.log('备份目录不存在');
      return [];
    }

    const files = fs.readdirSync(this.backupDir);
    const backups = files
      .filter(file => file.endsWith('.db'))
      .map(file => {
        const filePath = path.join(this.backupDir, file);
        const stats = fs.statSync(filePath);
        return {
          name: file,
          path: filePath,
          size: stats.size,
          created: stats.birthtime
        };
      })
      .sort((a, b) => b.created.getTime() - a.created.getTime());

    console.log('=== 备份文件列表 ===');
    if (backups.length === 0) {
      console.log('无备份文件');
    } else {
      backups.forEach(backup => {
        console.log(`${backup.name} (${backup.size} bytes, ${backup.created})`);
      });
    }

    return backups;
  }

  async vacuum() {
    console.log('优化数据库...');
    const db = await open({
      filename: this.dbPath,
      driver: sqlite3.Database
    });

    await db.exec('VACUUM');
    await db.close();
    console.log('✅ 数据库优化完成');
  }
}

// 命令行接口
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  const manager = new DatabaseManager();
  await manager.init();

  try {
    switch (command) {
      case 'create':
        await manager.createDatabase();
        break;
      case 'backup':
        await manager.backup(args[1]);
        break;
      case 'restore':
        if (!args[1]) {
          console.log('请指定备份文件路径');
          process.exit(1);
        }
        await manager.restore(args[1]);
        break;
      case 'analyze':
        await manager.analyze();
        break;
      case 'list-backups':
        await manager.listBackups();
        break;
      case 'vacuum':
        await manager.vacuum();
        break;
      default:
        console.log(`
数据库管理工具

用法:
  node db-manager.js <command> [options]

命令:
  create              创建新数据库
  backup [name]       备份数据库
  restore <path>      恢复数据库
  analyze             分析数据库
  list-backups        列出备份文件
  vacuum              优化数据库

示例:
  node db-manager.js create
  node db-manager.js backup my-backup
  node db-manager.js restore ./db/backups/backup-2025-06-08.db
  node db-manager.js analyze
        `);
    }
  } catch (error) {
    console.error('❌ 操作失败:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = DatabaseManager;
