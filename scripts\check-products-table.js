const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

// 数据库文件路径
const dbPath = path.resolve(process.cwd(), 'database.sqlite');

console.log(`检查products表: ${dbPath}`);

// 检查数据库文件是否存在
if (!fs.existsSync(dbPath)) {
  console.log('数据库文件不存在');
  process.exit(1);
}

// 创建数据库连接
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('连接数据库失败:', err.message);
    process.exit(1);
  }
  console.log('已连接到数据库');
});

// 检查products表结构
db.all("PRAGMA table_info(products)", (err, columns) => {
  if (err) {
    console.error('查询表结构失败:', err.message);
    db.close();
    process.exit(1);
  }
  
  console.log('products表结构:');
  columns.forEach(column => {
    console.log(`  - ${column.name} (${column.type})`);
  });
  
  // 查询products表中的数据
  db.all("SELECT * FROM products LIMIT 3", (err, rows) => {
    if (err) {
      console.error('查询数据失败:', err.message);
    } else {
      console.log('products表中的数据示例:');
      console.log(rows);
    }
    
    // 关闭数据库连接
    db.close((err) => {
      if (err) {
        console.error('关闭数据库失败:', err.message);
        process.exit(1);
      }
      console.log('数据库连接已关闭');
    });
  });
});
