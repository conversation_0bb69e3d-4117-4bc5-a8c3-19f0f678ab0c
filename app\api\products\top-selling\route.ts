import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';

// 获取热销药品
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const period = searchParams.get('period') || 'month';
    const limit = parseInt(searchParams.get('limit') || '5');

    let dateFilter = '';
    const now = new Date();

    // 根据时间段设置过滤条件 - 使用英文表名
    if (period === 'week') {
      // 过去一周
      const lastWeek = new Date(now);
      lastWeek.setDate(lastWeek.getDate() - 7);
      dateFilter = `AND date(o.created_at) >= '${lastWeek.toISOString().split('T')[0]}'`;
    } else if (period === 'month') {
      // 过去一个月
      const lastMonth = new Date(now);
      lastMonth.setMonth(lastMonth.getMonth() - 1);
      dateFilter = `AND date(o.created_at) >= '${lastMonth.toISOString().split('T')[0]}'`;
    } else if (period === 'year') {
      // 过去一年
      const lastYear = new Date(now);
      lastYear.setFullYear(lastYear.getFullYear() - 1);
      dateFilter = `AND date(o.created_at) >= '${lastYear.toISOString().split('T')[0]}'`;
    }

    // 检查是否有订单 - 使用英文表名
    const orderCount = await query(`
      SELECT COUNT(*) as count
      FROM orders
      WHERE orders.status = 'completed' ${dateFilter}
    `);

    let topProducts = [];

    // 只有当有订单时才查询热销药品
    if (orderCount[0].count > 0) {
      topProducts = await query(`
        SELECT
          p.id as id,
          p.name as name,
          SUM(od.quantity) as count,
          SUM(od.subtotal) as amount
        FROM order_details od
        JOIN products p ON od.product_id = p.id
        JOIN orders o ON od.order_id = o.id
        WHERE o.status = 'completed' ${dateFilter}
        GROUP BY p.id
        ORDER BY count DESC
        LIMIT ?
      `, [limit]);
    }

    return NextResponse.json({
      success: true,
      data: topProducts
    });
  } catch (error) {
    console.error('获取热销药品失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '获取热销药品失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
