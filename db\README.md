# 药店零售管理系统数据库完整分析报告

本文档提供了药店零售管理系统数据库的完整分析，包括数据库文件状态、表结构、数据分布和管理建议。

## 📊 数据库文件现状分析

### 发现的数据库文件

经过完整分析，项目中发现以下数据库文件：

| 文件路径 | 大小 | 表数量 | 总记录数 | 最后修改时间 | 状态 |
|---------|------|--------|----------|-------------|------|
| `./database.db` | 49,152 bytes | 9 | 30 | 2025-06-08 17:46:37 | **当前使用** |
| `./database.sqlite` | 77,824 bytes | 11 | 54 | 2025-05-25 22:12:20 | 历史数据 |
| `./db/retail.db` | 180,224 bytes | 18 | 28 | 2025-05-02 23:27:04 | 完整架构 |

### 当前活跃数据库：`./database.db`

**表结构和数据分布：**
- `settings` (6 条记录) - 系统设置
- `categories` (5 条记录) - 药品分类
- `products` (5 条记录) - 药品信息
- `customers` (5 条记录) - 客户信息
- `orders` (1 条记录) - 销售订单
- `order_details` (2 条记录) - 订单明细
- `inventory_records` (0 条记录) - 库存记录
- `suppliers` (0 条记录) - 供应商
- `sqlite_sequence` (6 条记录) - 自增序列

## 🗂️ 数据库脚本文件分析

### 发现的脚本文件（25个）

**初始化脚本：**
- `scripts/init-db.js` (9,874 bytes) - 中文表名初始化
- `app/db/init.ts` (2,845 bytes) - 英文表名初始化
- `scripts/create-full-database.js` (8,234 bytes) - 完整数据库创建

**架构定义：**
- `db/schema.sql` (21,436 bytes) - 完整数据库架构（中文表名）
- `db/suppliers.sql` (2,545 bytes) - 供应商表定义

**数据修复脚本：**
- `scripts/fix-database-data.js` (8,747 bytes) - 数据修复
- `scripts/modify-products-table.js` (6,091 bytes) - 产品表修改
- `scripts/update-products-table.js` (5,986 bytes) - 产品表更新

**功能增强脚本：**
- `scripts/add-mashangfangxin-settings.js` (4,708 bytes) - 码上放心设置
- `scripts/add-payment-fields.js` (3,373 bytes) - 支付字段添加

## 🔧 数据库连接配置

**当前配置（lib/db.ts）：**
```typescript
const dbPath = './database.db';  // 当前使用的数据库
```

**其他连接配置：**
- `app/db/connection.ts` → `database.sqlite`
- `app/db/init.ts` → `database.sqlite`

## ⚠️ 发现的问题

### 1. 数据库文件分散
- 3个不同的数据库文件存在于不同位置
- 不同的代码文件连接不同的数据库
- 缺乏统一的数据库管理策略

### 2. 表结构不一致
- `database.db` 使用英文表名，字段较少
- `database.sqlite` 使用英文表名，字段较多
- `db/retail.db` 使用中文表名，架构最完整

### 3. 数据分布不均
- 主要业务数据分散在不同数据库中
- 部分表为空或数据不完整

## 📋 建议的解决方案

### 1. 统一数据库文件位置
**目标：** 将所有数据库文件统一到 `db/` 目录

### 2. 标准化表结构
**建议使用英文表名，基于以下考虑：**
- 代码兼容性更好
- 避免编码问题
- 便于国际化

### 3. 数据迁移计划
1. 备份现有数据
2. 创建统一的数据库架构
3. 迁移数据到新数据库
4. 更新连接配置

## 📁 推荐的目录结构

```
db/
├── README.md                 # 本文档
├── main.db                   # 主数据库文件
├── schema.sql               # 数据库架构定义
├── init/                    # 初始化脚本
│   ├── create-tables.sql    # 创建表
│   ├── insert-data.sql      # 初始数据
│   └── create-indexes.sql   # 创建索引
├── migrations/              # 数据库迁移脚本
│   ├── 001-initial.sql
│   ├── 002-add-trace-code.sql
│   └── 003-add-payment-fields.sql
└── backups/                 # 数据库备份
    └── backup-YYYY-MM-DD.db
```

## 🗃️ 详细表结构分析

### 核心业务表

#### 1. products（药品表）
**当前字段：**
- `id` - 主键
- `name` - 药品名称
- `generic_name` - 通用名
- `description` - 描述 ✅ **已修复**
- `barcode` - 条形码
- `trace_code` - 追溯码 ✅ **已修复**
- `category_id` - 分类ID
- `manufacturer` - 生产厂家
- `approval_number` - 批准文号 ✅ **已修复**
- `specification` - 规格
- `dosage_form` - 剂型 ✅ **已修复**
- `price` - 售价
- `cost_price` - 成本价
- `stock_quantity` - 库存数量
- `min_stock_level` - 最低库存 ✅ **已修复**
- `is_prescription` - 是否处方药 ✅ **已修复**
- `is_medical_insurance` - 是否医保 ✅ **已修复**
- `storage_condition` - 储存条件
- `status` - 状态
- `created_at` - 创建时间
- `updated_at` - 更新时间

#### 2. categories（分类表）
- `id` - 主键
- `name` - 分类名称
- `description` - 描述
- `created_at` - 创建时间
- `updated_at` - 更新时间

#### 3. orders（订单表）
- `id` - 主键
- `order_number` - 订单编号
- `customer_id` - 客户ID
- `total_amount` - 总金额
- `discount_amount` - 折扣金额
- `payable_amount` - 应付金额
- `paid_amount` - 实付金额
- `change_amount` - 找零金额
- `payment_method` - 支付方式
- `status` - 订单状态
- `created_at` - 创建时间
- `updated_at` - 更新时间

#### 4. order_details（订单明细表）
- `id` - 主键
- `order_id` - 订单ID
- `product_id` - 药品ID
- `quantity` - 数量
- `unit_price` - 单价
- `total_price` - 小计
- `created_at` - 创建时间

### 支持表

#### 5. customers（客户表）
- `id` - 主键
- `name` - 客户姓名
- `phone` - 电话
- `email` - 邮箱
- `address` - 地址
- `id_card` - 身份证号
- `medical_card` - 医保卡号
- `member_level` - 会员等级
- `points` - 积分
- `created_at` - 创建时间
- `updated_at` - 更新时间

#### 6. suppliers（供应商表）
- `id` - 主键
- `name` - 供应商名称
- `contact_person` - 联系人
- `phone` - 电话
- `email` - 邮箱
- `address` - 地址
- `tax_number` - 税号
- `bank_account` - 银行账户
- `bank_name` - 开户行
- `status` - 状态
- `created_at` - 创建时间
- `updated_at` - 更新时间

#### 7. inventory_records（库存记录表）
- `id` - 主键
- `product_id` - 药品ID
- `quantity` - 变动数量
- `type` - 变动类型（in/out）
- `reference_number` - 参考单号
- `supplier_id` - 供应商ID
- `note` - 备注
- `created_at` - 创建时间

#### 8. settings（系统设置表）
- `id` - 主键
- `setting_name` - 设置名称
- `setting_value` - 设置值
- `description` - 描述
- `created_at` - 创建时间
- `updated_at` - 更新时间

## 🔄 数据迁移状态

### 已完成的修复
✅ **products表字段修复**（2025-06-08）
- 添加 `description` 字段
- 添加 `trace_code` 字段
- 添加 `approval_number` 字段
- 添加 `dosage_form` 字段
- 添加 `min_stock_level` 字段
- 添加 `is_prescription` 字段
- 添加 `is_medical_insurance` 字段

### 待处理的问题
⚠️ **数据库文件统一**
- 需要将数据库文件统一到db目录
- 需要更新连接配置

⚠️ **数据完整性**
- inventory_records表无数据
- suppliers表无数据
- 部分订单数据不完整

## 🛠️ 维护工具

### 数据库管理脚本
- `analyze-databases.js` - 数据库分析工具
- `reset_db.ps1` - 数据库重置脚本（PowerShell）

### API管理接口
- `/api/db-management` - 数据库管理API
- `/api/db-test` - 数据库连接测试
- `/api/init-db` - 数据库初始化

## 📞 技术支持

如需数据库相关技术支持，请参考：
1. 本README文档
2. `database-analysis.json` 分析报告
3. 相关API接口文档

---

**最后更新：** 2025-06-08
**分析工具：** analyze-databases.js
**数据库版本：** SQLite 3.x

// 执行更新操作
await run('UPDATE products SET price = ? WHERE id = ?', [10.99, 1]);
```

## 数据库管理

可以使用SQLite命令行工具管理数据库：

```bash
# 连接到数据库
sqlite3 db/retail.db

# 查看所有表
.tables

# 查看表结构
.schema products

# 执行SQL查询
SELECT * FROM products LIMIT 5;

# 退出
.exit
```

## 重置数据库

如需重置数据库，可以删除 `retail.db` 文件并重新执行初始化脚本：

```bash
sqlite3 db/retail.db ".read db/schema.sql"
```