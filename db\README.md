# 药店零售管理系统数据库

本目录包含药店零售管理系统使用的SQLite数据库文件和相关脚本。

## 数据库文件

- `retail.db`: 主数据库文件，包含所有系统数据
- `schema.sql`: 数据库架构定义脚本

## 数据库结构

系统包含以下主要表：

1. **users** - 系统用户
2. **categories** - 药品分类
3. **products** - 药品信息
4. **customers** - 客户信息
5. **orders** - 销售订单
6. **order_items** - 订单明细
7. **inventory_transactions** - 库存变动记录
8. **suppliers** - 供应商
9. **purchase_orders** - 采购订单
10. **purchase_order_items** - 采购订单明细

## 初始账户

系统已预设一个管理员账户：
- 用户名: admin
- 密码: admin123

## 在应用中使用数据库

系统使用 `lib/db.ts` 提供的工具函数连接和操作数据库：

```typescript
import { query, get, run } from '@/lib/db';

// 查询多条记录
const products = await query('SELECT * FROM products LIMIT 10');

// 查询单条记录
const product = await get('SELECT * FROM products WHERE id = ?', [1]);

// 执行更新操作
await run('UPDATE products SET price = ? WHERE id = ?', [10.99, 1]);
```

## 数据库管理

可以使用SQLite命令行工具管理数据库：

```bash
# 连接到数据库
sqlite3 db/retail.db

# 查看所有表
.tables

# 查看表结构
.schema products

# 执行SQL查询
SELECT * FROM products LIMIT 5;

# 退出
.exit
```

## 重置数据库

如需重置数据库，可以删除 `retail.db` 文件并重新执行初始化脚本：

```bash
sqlite3 db/retail.db ".read db/schema.sql"
```