import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

interface StockOutRequest {
  product_id: number;
  quantity: number;
  reason: string;
  stock_out_date: string;
  notes?: string;
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json() as StockOutRequest;
    const { product_id, quantity, reason, stock_out_date, notes } = body;
    
    // 验证数据
    if (!product_id || !quantity || !reason || !stock_out_date) {
      return NextResponse.json({ 
        success: false, 
        message: '缺少必要参数' 
      }, { status: 400 });
    }

    // 1. 获取产品信息
    const product = await prisma.product.findUnique({
      where: { id: product_id }
    });

    if (!product) {
      return NextResponse.json({ 
        success: false, 
        message: '未找到产品' 
      }, { status: 404 });
    }

    // 2. 检查库存是否足够
    if (product.stock_quantity < quantity) {
      return NextResponse.json({ 
        success: false, 
        message: '库存不足' 
      }, { status: 400 });
    }

    // 3. 创建出库记录
    const stockOut = await prisma.stockOut.create({
      data: {
        product_id,
        quantity,
        reason,
        stock_out_date: new Date(stock_out_date),
        notes: notes || '',
      }
    });

    // 4. 更新产品库存
    const updatedProduct = await prisma.product.update({
      where: { id: product_id },
      data: {
        stock_quantity: {
          decrement: quantity
        }
      }
    });

    return NextResponse.json({ 
      success: true, 
      data: {
        stock_out: stockOut,
        product: updatedProduct
      }
    });
  } catch (error) {
    console.error('出库处理错误:', error);
    return NextResponse.json({ 
      success: false, 
      message: '服务器处理出库请求时出错' 
    }, { status: 500 });
  }
} 