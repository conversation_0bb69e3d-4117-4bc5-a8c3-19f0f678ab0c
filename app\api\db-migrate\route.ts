import { NextResponse } from 'next/server';
import { Database } from 'sqlite3';
import { open } from 'sqlite';

// 数据库迁移API路由
export async function GET() {
  try {
    console.log('开始执行数据库迁移...');
    
    // 打开数据库连接
    const db = await open({
      filename: './database.sqlite',
      driver: Database
    });
    
    console.log('数据库连接成功');
    
    // 检查products表是否存在trace_code列
    const tableInfo = await db.all(`PRAGMA table_info(products)`);
    const hasTraceCodeColumn = tableInfo.some(column => column.name === 'trace_code');
    
    if (!hasTraceCodeColumn) {
      console.log('添加trace_code列到products表...');
      
      // 添加trace_code列
      await db.exec(`ALTER TABLE products ADD COLUMN trace_code TEXT`);
      
      console.log('trace_code列添加成功');
    } else {
      console.log('trace_code列已存在，无需添加');
    }
    
    // 关闭数据库连接
    await db.close();
    
    return NextResponse.json({
      success: true,
      message: '数据库迁移成功完成'
    });
  } catch (error) {
    console.error('数据库迁移失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '数据库迁移失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
