import { NextRequest, NextResponse } from 'next/server';
import { query, run } from '@/lib/db';

// 获取药品列表
export async function GET() {
  try {
    const products = await query(
      `SELECT
        p.id as id,
        p.name as name,
        p.generic_name as generic_name,
        p.description as description,
        p.barcode as barcode,
        p.trace_code as trace_code,
        p.category_id as category_id,
        c.name as category_name,
        p.manufacturer as manufacturer,
        p.approval_number as approval_number,
        p.specification as specification,
        p.dosage_form as dosage_form,
        p.price as price,
        p.cost_price as cost_price,
        p.stock_quantity as stock_quantity,
        p.min_stock_level as min_stock_level,
        p.is_prescription as is_prescription,
        p.is_medical_insurance as is_medical_insurance,
        p.storage_condition as storage_condition,
        p.status as status
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      ORDER BY p.id DESC`
    );

    // 处理布尔值转换
    const processedProducts = products.map(product => ({
      ...product,
      is_prescription: product.is_prescription === 1,
      is_medical_insurance: product.is_medical_insurance === 1
    }));

    return NextResponse.json({
      success: true,
      data: processedProducts
    });
  } catch (error) {
    console.error('获取药品列表失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '获取药品列表失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

// 创建新药品
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    console.log('POST接收到的数据:', data);

    const {
      name,
      generic_name,
      description,
      barcode,
      trace_code,
      category_id,
      manufacturer,
      approval_number,
      specification,
      dosage_form,
      price,
      cost_price,
      stock_quantity,
      min_stock_level,
      is_prescription,
      is_medical_insurance,
      storage_condition,
      status
    } = data;

    // 验证必填字段
    const missingFields = [];
    if (!name) missingFields.push('药品名称');
    if (!category_id) missingFields.push('药品分类');
    if (!manufacturer) missingFields.push('生产厂家');
    if (!specification) missingFields.push('规格');
    if (!dosage_form) missingFields.push('剂型');
    if (price === undefined || price === null) missingFields.push('销售价格');

    if (missingFields.length > 0) {
      return NextResponse.json(
        {
          success: false,
          message: '缺少必填字段',
          error: `请填写以下必填项: ${missingFields.join(', ')}`
        },
        { status: 400 }
      );
    }

    // 确保category_id是数字
    const categoryIdNum = typeof category_id === 'string' ? parseInt(category_id, 10) : category_id;

    // 检查分类是否存在
    const categoryCheck = await query('SELECT id FROM categories WHERE id = ?', [categoryIdNum]);
    if (!categoryCheck || categoryCheck.length === 0) {
      return NextResponse.json(
        {
          success: false,
          message: '选择的药品分类不存在',
          error: `分类ID ${categoryIdNum} 不存在`
        },
        { status: 400 }
      );
    }

    console.log('执行插入操作，参数:', [
      name, generic_name, description, barcode, trace_code, categoryIdNum,
      manufacturer, approval_number, specification, dosage_form,
      price, cost_price, stock_quantity, min_stock_level,
      is_prescription ? 1 : 0, is_medical_insurance ? 1 : 0, storage_condition, status
    ]);

    const result = await run(
      `INSERT INTO products (
        name, generic_name, description, barcode, trace_code, category_id,
        manufacturer, approval_number, specification, dosage_form,
        price, cost_price, stock_quantity, min_stock_level,
        is_prescription, is_medical_insurance, storage_condition, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        name, generic_name, description, barcode, trace_code, categoryIdNum,
        manufacturer, approval_number, specification, dosage_form,
        price, cost_price, stock_quantity, min_stock_level,
        is_prescription ? 1 : 0, is_medical_insurance ? 1 : 0, storage_condition, status
      ]
    );

    console.log('插入结果:', result);

    if (!result.lastID) {
      return NextResponse.json(
        {
          success: false,
          message: '创建药品失败',
          error: '数据库操作未返回新记录ID'
        },
        { status: 500 }
      );
    }

    // 记录库存变动
    if (stock_quantity > 0) {
      const today = new Date().toISOString().split('T')[0]; // 获取当前日期，格式为YYYY-MM-DD

      try {
        // 创建一个批次记录
        const batchResult = await run(
          `INSERT INTO product_batches (
            product_id, batch_number, production_date, expiry_date, quantity, supplier_id, cost_price
          ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
          [
            result.lastID,
            `INIT-${result.lastID}`, // 初始批次号
            today, // 当前日期作为生产日期
            new Date(new Date().setFullYear(new Date().getFullYear() + 2)).toISOString().split('T')[0], // 2年后作为有效期
            stock_quantity,
            1, // 默认供应商ID，实际应用中可能需要调整
            cost_price || 0
          ]
        );

        console.log('批次创建结果:', batchResult);

        // 记录库存变动
        await run(
          `INSERT INTO inventory_records (
            product_id, batch_id, quantity_change, change_type, note, operator_id
          ) VALUES (?, ?, ?, ?, ?, ?)`,
          [
            result.lastID,
            batchResult.lastID,
            stock_quantity,
            'adjustment',
            '初始库存录入',
            1 // 默认操作员ID
          ]
        );
      } catch (batchError) {
        console.error('创建批次或库存记录失败:', batchError);
        // 继续执行，不影响药品创建的成功状态
      }
    }

    const newProduct = await query(
      `SELECT
        id,
        name,
        generic_name,
        description,
        barcode,
        trace_code,
        category_id,
        manufacturer,
        approval_number,
        specification,
        dosage_form,
        price,
        cost_price,
        stock_quantity,
        min_stock_level,
        is_prescription,
        is_medical_insurance,
        storage_condition,
        status
      FROM products WHERE id = ?`,
      [result.lastID]
    );

    if (!newProduct || newProduct.length === 0) {
      return NextResponse.json(
        {
          success: false,
          message: '创建药品后无法查询到新药品',
          error: `无法查询ID为 ${result.lastID} 的药品`
        },
        { status: 500 }
      );
    }

    // 处理布尔值转换
    const processedProduct = {
      ...newProduct[0],
      is_prescription: newProduct[0].is_prescription === 1,
      is_medical_insurance: newProduct[0].is_medical_insurance === 1
    };

    return NextResponse.json({
      success: true,
      data: processedProduct
    });
  } catch (error) {
    console.error('创建药品失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '创建药品失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

// 更新药品信息
export async function PUT(request: NextRequest) {
  try {
    const data = await request.json();
    console.log('PUT接收到的数据:', data);

    const {
      id,
      name,
      generic_name,
      description,
      barcode,
      trace_code,
      category_id,
      manufacturer,
      approval_number,
      specification,
      dosage_form,
      price,
      cost_price,
      min_stock_level,
      is_prescription,
      is_medical_insurance,
      storage_condition,
      status
    } = data;

    // 验证必填字段
    if (!id || !name || !category_id) {
      return NextResponse.json(
        {
          success: false,
          message: '缺少必填字段',
          error: `药品ID、名称和分类是必填项`
        },
        { status: 400 }
      );
    }

    // 确保category_id是数字
    const categoryIdNum = typeof category_id === 'string' ? parseInt(category_id, 10) : category_id;

    // 检查药品是否存在
    const productCheck = await query('SELECT id FROM products WHERE id = ?', [id]);
    if (!productCheck || productCheck.length === 0) {
      return NextResponse.json(
        {
          success: false,
          message: '药品不存在',
          error: `ID为 ${id} 的药品不存在`
        },
        { status: 404 }
      );
    }

    // 检查分类是否存在
    const categoryCheck = await query('SELECT id FROM categories WHERE id = ?', [categoryIdNum]);
    if (!categoryCheck || categoryCheck.length === 0) {
      return NextResponse.json(
        {
          success: false,
          message: '选择的药品分类不存在',
          error: `分类ID ${categoryIdNum} 不存在`
        },
        { status: 400 }
      );
    }

    console.log('执行更新操作，参数:', [
      name, generic_name, description, barcode, trace_code, categoryIdNum,
      manufacturer, approval_number, specification, dosage_form,
      price, cost_price, min_stock_level,
      is_prescription ? 1 : 0, is_medical_insurance ? 1 : 0, storage_condition, status, id
    ]);

    const result = await run(
      `UPDATE products
      SET
        name = ?,
        generic_name = ?,
        description = ?,
        barcode = ?,
        trace_code = ?,
        category_id = ?,
        manufacturer = ?,
        approval_number = ?,
        specification = ?,
        dosage_form = ?,
        price = ?,
        cost_price = ?,
        min_stock_level = ?,
        is_prescription = ?,
        is_medical_insurance = ?,
        storage_condition = ?,
        status = ?,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ?`,
      [
        name, generic_name, description, barcode, trace_code, categoryIdNum,
        manufacturer, approval_number, specification, dosage_form,
        price, cost_price, min_stock_level,
        is_prescription ? 1 : 0, is_medical_insurance ? 1 : 0, storage_condition, status, id
      ]
    );

    console.log('更新结果:', result);

    const updatedProduct = await query(
      `SELECT
        id,
        name,
        generic_name,
        description,
        barcode,
        trace_code,
        category_id,
        manufacturer,
        approval_number,
        specification,
        dosage_form,
        price,
        cost_price,
        stock_quantity,
        min_stock_level,
        is_prescription,
        is_medical_insurance,
        storage_condition,
        status
      FROM products WHERE id = ?`,
      [id]
    );

    if (!updatedProduct || updatedProduct.length === 0) {
      return NextResponse.json(
        {
          success: false,
          message: '更新后无法查询到药品',
          error: `无法查询ID为 ${id} 的药品`
        },
        { status: 404 }
      );
    }

    // 处理布尔值转换
    const processedProduct = {
      ...updatedProduct[0],
      is_prescription: updatedProduct[0].is_prescription === 1,
      is_medical_insurance: updatedProduct[0].is_medical_insurance === 1
    };

    return NextResponse.json({
      success: true,
      data: processedProduct
    });
  } catch (error) {
    console.error('更新药品失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '更新药品失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

// 删除药品
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { success: false, message: '药品ID不能为空' },
        { status: 400 }
      );
    }

    // 检查药品是否存在
    const checkResult = await query(
      'SELECT id FROM products WHERE id = ?',
      [id]
    );

    if (!checkResult || checkResult.length === 0) {
      return NextResponse.json(
        { success: false, message: '药品不存在' },
        { status: 404 }
      );
    }

    // 检查是否有关联的订单明细
    const orderDetails = await query('SELECT id FROM order_details WHERE product_id = ? LIMIT 1', [id]);
    if (orderDetails && orderDetails.length > 0) {
      // 如果有关联的订单明细，则只将状态设置为inactive
      await run('UPDATE products SET status = "inactive", updated_at = CURRENT_TIMESTAMP WHERE id = ?', [id]);
    } else {
      // 如果没有关联的订单明细，则可以删除药品
      await run('DELETE FROM product_batches WHERE product_id = ?', [id]);
      await run('DELETE FROM inventory_records WHERE product_id = ?', [id]);
      await run('DELETE FROM products WHERE id = ?', [id]);
    }

    return NextResponse.json({
      success: true,
      message: '药品删除成功'
    });
  } catch (error) {
    console.error('删除药品失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '删除药品失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}