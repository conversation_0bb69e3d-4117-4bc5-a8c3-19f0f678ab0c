const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// 数据库文件路径
const dbPath = path.join(__dirname, '..', 'database.sqlite');
console.log('数据库路径:', dbPath);

const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('连接数据库失败:', err.message);
    return;
  }
  console.log('已连接到数据库');
});

// 更新表结构
db.serialize(() => {
  // 为customers表添加缺失的字段
  db.run(`ALTER TABLE customers ADD COLUMN is_member INTEGER DEFAULT 0`, (err) => {
    if (err && !err.message.includes('duplicate column name')) {
      console.error('添加is_member字段失败:', err);
    } else {
      console.log('customers表is_member字段已添加或已存在');
    }
  });

  db.run(`ALTER TABLE customers ADD COLUMN member_number TEXT`, (err) => {
    if (err && !err.message.includes('duplicate column name')) {
      console.error('添加member_number字段失败:', err);
    } else {
      console.log('customers表member_number字段已添加或已存在');
    }
  });

  // 为orders表添加缺失的字段
  db.run(`ALTER TABLE orders ADD COLUMN discount_amount REAL DEFAULT 0`, (err) => {
    if (err && !err.message.includes('duplicate column name')) {
      console.error('添加discount_amount字段失败:', err);
    } else {
      console.log('orders表discount_amount字段已添加或已存在');
    }
  });

  db.run(`ALTER TABLE orders ADD COLUMN payable_amount REAL DEFAULT 0`, (err) => {
    if (err && !err.message.includes('duplicate column name')) {
      console.error('添加payable_amount字段失败:', err);
    } else {
      console.log('orders表payable_amount字段已添加或已存在');
    }
  });

  db.run(`ALTER TABLE orders ADD COLUMN received_amount REAL DEFAULT 0`, (err) => {
    if (err && !err.message.includes('duplicate column name')) {
      console.error('添加received_amount字段失败:', err);
    } else {
      console.log('orders表received_amount字段已添加或已存在');
    }
  });

  db.run(`ALTER TABLE orders ADD COLUMN change_amount REAL DEFAULT 0`, (err) => {
    if (err && !err.message.includes('duplicate column name')) {
      console.error('添加change_amount字段失败:', err);
    } else {
      console.log('orders表change_amount字段已添加或已存在');
      
      // 关闭数据库连接
      db.close((err) => {
        if (err) {
          console.error('关闭数据库失败:', err.message);
        } else {
          console.log('数据库连接已关闭');
        }
      });
    }
  });
});
