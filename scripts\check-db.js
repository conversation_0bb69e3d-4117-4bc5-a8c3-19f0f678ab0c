const fs = require('fs');
const path = require('path');

// 检查数据库文件
const dbPath1 = path.resolve(process.cwd(), 'database.sqlite');
const dbPath2 = path.resolve(process.cwd(), 'db/retail.db');

console.log('检查数据库文件:');
console.log(`database.sqlite: ${fs.existsSync(dbPath1) ? '存在' : '不存在'}`);
console.log(`db/retail.db: ${fs.existsSync(dbPath2) ? '存在' : '不存在'}`);

// 如果db目录不存在，创建它
const dbDir = path.resolve(process.cwd(), 'db');
if (!fs.existsSync(dbDir)) {
  console.log('创建db目录');
  fs.mkdirSync(dbDir);
}

// 如果retail.db不存在，创建一个空文件
if (!fs.existsSync(dbPath2)) {
  console.log('创建空的retail.db文件');
  fs.writeFileSync(dbPath2, '');
}

console.log('检查完成');
