const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

// 数据库文件路径
const dbPath = path.resolve(process.cwd(), 'database.sqlite');

console.log(`向categories表插入数据: ${dbPath}`);

// 检查数据库文件是否存在
if (!fs.existsSync(dbPath)) {
  console.log('数据库文件不存在');
  process.exit(1);
}

// 创建数据库连接
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('连接数据库失败:', err.message);
    process.exit(1);
  }
  console.log('已连接到数据库');
});

// 检查categories表中是否已有数据
db.get("SELECT COUNT(*) as count FROM categories", (err, row) => {
  if (err) {
    console.error('查询表失败:', err.message);
    db.close();
    process.exit(1);
  }
  
  if (row && row.count > 0) {
    console.log(`categories表中已有${row.count}条数据，跳过插入操作`);
    db.close();
    return;
  }
  
  console.log('categories表中没有数据，开始插入...');
  
  // 插入药品分类数据
  const categories = [
    { name: '中成药', description: '传统中成药制剂' },
    { name: '化学药品', description: '西药、化学药品' },
    { name: '中药材', description: '中药材、饮片' },
    { name: '生物制品', description: '疫苗、血液制品等' },
    { name: '医疗器械', description: '医疗器械、耗材' },
    { name: '保健品', description: '保健食品、功能性食品' },
    { name: '感冒用药', description: '治疗感冒相关症状' },
    { name: '解热镇痛', description: '缓解疼痛和发热' },
    { name: '抗生素', description: '抗菌药物' },
    { name: '维生素类', description: '补充维生素' },
    { name: '清热解毒', description: '清热解毒类中药' }
  ];
  
  // 使用事务进行批量插入
  db.serialize(() => {
    db.run('BEGIN TRANSACTION');
    
    const stmt = db.prepare('INSERT INTO categories (name, description) VALUES (?, ?)');
    
    categories.forEach(category => {
      stmt.run(category.name, category.description, (err) => {
        if (err) {
          console.error(`插入分类 "${category.name}" 失败:`, err.message);
        }
      });
    });
    
    stmt.finalize();
    
    db.run('COMMIT', (err) => {
      if (err) {
        console.error('提交事务失败:', err.message);
      } else {
        console.log(`成功插入 ${categories.length} 条分类数据`);
      }
      
      // 查询插入后的数据
      db.all("SELECT * FROM categories", (err, rows) => {
        if (err) {
          console.error('查询数据失败:', err.message);
        } else {
          console.log('categories表中的数据:');
          console.log(rows);
        }
        
        // 关闭数据库连接
        db.close((err) => {
          if (err) {
            console.error('关闭数据库失败:', err.message);
            process.exit(1);
          }
          console.log('数据库连接已关闭');
        });
      });
    });
  });
});
