import { NextResponse } from 'next/server';
import { query } from '@/lib/db';

// 获取药品分类列表
export async function GET() {
  try {
    const categories = await query(
      'SELECT id, name, description FROM categories ORDER BY id ASC'
    );

    return NextResponse.json({
      success: true,
      data: categories
    });
  } catch (error) {
    console.error('获取药品分类列表失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '获取药品分类列表失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}